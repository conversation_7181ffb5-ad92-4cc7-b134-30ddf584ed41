<!DOCTYPE html>
<html lang="zh-TW">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>簡易棒球遊戲</title>
<style>
  body {
    font-family: 'Noto Sans TC', sans-serif, Arial, sans-serif;
    background-color: #f0f8ff;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
  }
  #game-container {
    background-color: #fff;
    padding: 20px 40px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    width: 500px;
  }
  h1 {
    color: #005a9c;
  }
  #scoreboard {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    background-color: #e0e0e0;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    font-size: 1.2em;
    font-weight: bold;
  }
  .score-item span {
    display: block;
    font-size: 0.8em;
    color: #555;
  }
  #diamond {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 40px auto;
    transform: rotate(45deg);
  }
  .base {
    position: absolute;
    width: 30px;
    height: 30px;
    background-color: #ccc;
    border: 2px solid #888;
  }
  #home-plate { bottom: -15px; left: 85px; transform: rotate(-45deg); width: 30px; height: 30px; background-color: #fff; border: 2px solid #888;}
  #first-base { top: 85px; right: -15px; }
  #second-base { top: -15px; left: 85px; }
  #third-base { bottom: 85px; left: -15px; }
  .occupied {
    background-color: #ffeb3b; /* Yellow when occupied */
  }
  #game-log {
    margin-top: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 5px;
    height: 50px;
    overflow-y: auto;
  }
  #swing-button {
    background-color: #4CAF50; /* Green */
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    margin-top: 20px;
    transition: background-color 0.3s;
  }
  #swing-button:hover {
    background-color: #45a049;
  }
</style>
</head>
<body>

<div id="game-container">
  <h1>棒球對決</h1>
  
  <div id="scoreboard">
    <div class="score-item"><span>分數</span><p id="score">0</p></div>
    <div class="score-item"><span>局數</span><p id="inning">1</p></div>
    <div class="score-item"><span>好球</span><p id="strikes">0</p></div>
    <div class="score-item"><span>壞球</span><p id="balls">0</p></div>
    <div class="score-item"><span>出局</span><p id="outs">0</p></div>
  </div>

  <div id="diamond-container">
      <div id="diamond">
        <div id="home-plate-display"></div>
        <div class="base" id="first-base"></div>
        <div class="base" id="second-base"></div>
        <div class="base" id="third-base"></div>
      </div>
  </div>

  <div id="game-log">歡迎來到比賽！</div>
  
  <button id="swing-button">揮棒</button>
</div>

<script>
  // DOM Elements
  const scoreEl = document.getElementById('score');
  const inningEl = document.getElementById('inning');
  const strikesEl = document.getElementById('strikes');
  const ballsEl = document.getElementById('balls');
  const outsEl = document.getElementById('outs');
  const gameLogEl = document.getElementById('game-log');
  const swingButton = document.getElementById('swing-button');
  const firstBaseEl = document.getElementById('first-base');
  const secondBaseEl = document.getElementById('second-base');
  const thirdBaseEl = document.getElementById('third-base');

  // Game State
  let score = 0;
  let inning = 1;
  let strikes = 0;
  let balls = 0;
  let outs = 0;
  let bases = [false, false, false]; // [first, second, third]

  // Event Listener
  swingButton.addEventListener('click', handleSwing);

  function handleSwing() {
    const outcome = Math.random(); // Generate a random number for the outcome
    
    // Determine outcome based on probability
    if (outcome < 0.4) { // 40% chance of a strike
      addStrike();
    } else if (outcome < 0.7) { // 30% chance of a ball
      addBall();
    } else if (outcome < 0.85) { // 15% chance of a single
      handleHit(1);
    } else if (outcome < 0.95) { // 10% chance of a double
      handleHit(2);
    } else if (outcome < 0.98) { // 3% chance of a triple
      handleHit(3);
    } else { // 2% chance of a Home Run
      handleHit(4);
    }
    updateDisplay();
  }

  function addStrike() {
    strikes++;
    logMessage("好球！");
    if (strikes === 3) {
      logMessage("三振出局！");
      handleOut();
    }
  }

  function addBall() {
    balls++;
    logMessage("壞球！");
    if (balls === 4) {
      logMessage("四壞保送！");
      handleWalk();
    }
  }

  function handleOut() {
    outs++;
    resetCount();
    if (outs === 3) {
      logMessage(`三出局，攻守交換！`);
      nextInning();
    }
  }

  function nextInning() {
    inning++;
    outs = 0;
    bases = [false, false, false];
    resetCount();
  }

  function resetCount() {
    strikes = 0;
    balls = 0;
  }

  function handleWalk() {
      // A walk is like a single, but only advances runners if forced.
      let newBases = [...bases];
      if (bases[0] && bases[1] && bases[2]) { // Bases loaded
          score++;
          logMessage("擠回一分！");
      } else if (bases[0] && bases[1]) { // Runners on 1st and 2nd
          newBases[2] = true;
      } else if (bases[0]) { // Runner on 1st
          newBases[1] = true;
      }
      newBases[0] = true;
      bases = newBases;
      resetCount();
  }

  function handleHit(numBases) {
    let runnersScored = 0;
    // Move existing runners
    for (let i = 2; i >= 0; i--) {
      if (bases[i]) {
        const newBase = i + numBases;
        if (newBase >= 3) {
          runnersScored++;
          score++;
        } else {
          bases[newBase] = true;
        }
        bases[i] = false;
      }
    }
    
    // Place the batter
    if (numBases < 4) {
      bases[numBases - 1] = true;
    } else { // Home run
      runnersScored++;
      score++;
    }

    const hitTypes = ["一壘安打", "二壘安打", "三壘安打", "全壘打"];
    logMessage(`${hitTypes[numBases-1]}！`);
    if(runnersScored > 0) {
        logMessage(`得了 ${runnersScored} 分！`);
    }
    resetCount();
  }

  function logMessage(message) {
    gameLogEl.textContent = message;
  }

  function updateDisplay() {
    scoreEl.textContent = score;
    inningEl.textContent = inning;
    strikesEl.textContent = strikes;
    ballsEl.textContent = balls;
    outsEl.textContent = outs;

    // Update bases visual
    firstBaseEl.classList.toggle('occupied', bases[0]);
    secondBaseEl.classList.toggle('occupied', bases[1]);
    thirdBaseEl.classList.toggle('occupied', bases[2]);

    if (inning > 9) {
        swingButton.disabled = true;
        logMessage("比賽結束！最終比分：" + score);
    }
  }

  // Initial display update
  updateDisplay();
</script>

</body>
</html>