#!/usr/bin/env python3
"""
修復Three.js下載問題 - 使用正確的CDN地址
"""

import urllib.request
import os
import shutil

def download_threejs_correct():
    """從正確的CDN下載Three.js"""
    
    # 使用r149版本，這是最後一個穩定的傳統版本
    cdn_urls = [
        "https://unpkg.com/three@0.149.0/build/three.min.js",
        "https://cdn.jsdelivr.net/npm/three@0.149.0/build/three.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/three.js/r149/three.min.js"
    ]
    
    # 確保js目錄存在
    os.makedirs("js", exist_ok=True)
    local_path = "js/three.min.js"
    
    print("🔄 重新下載正確的Three.js...")
    
    for i, url in enumerate(cdn_urls, 1):
        print(f"\n📥 嘗試源 {i}: {url}")
        try:
            # 設置User-Agent避免被阻擋
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            with urllib.request.urlopen(req) as response:
                content = response.read()
                
                # 檢查內容是否是真正的Three.js
                content_str = content.decode('utf-8', errors='ignore')
                if 'THREE' in content_str and len(content) > 100000:
                    with open(local_path, 'wb') as f:
                        f.write(content)
                    
                    file_size = len(content)
                    print(f"✅ 下載成功！文件大小: {file_size:,} 字節")
                    
                    # 驗證文件
                    if verify_threejs(local_path):
                        return True
                    else:
                        print("⚠️ 文件驗證失敗")
                        continue
                else:
                    print(f"⚠️ 下載的不是有效的Three.js文件")
                    continue
                    
        except Exception as e:
            print(f"❌ 下載失敗: {e}")
            continue
    
    return False

def verify_threejs(file_path):
    """驗證Three.js文件是否有效"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read(1000)  # 只讀前1000字符
            
        # 檢查是否包含Three.js的關鍵標識
        if 'THREE' in content and ('Scene' in content or 'Camera' in content):
            print("✅ Three.js文件驗證通過")
            return True
        else:
            print("❌ Three.js文件驗證失敗")
            return False
            
    except Exception as e:
        print(f"❌ 文件驗證錯誤: {e}")
        return False

def create_fallback_threejs():
    """創建一個最小的Three.js替代版本"""
    
    print("🔧 創建Three.js替代版本...")
    
    # 最小的Three.js核心功能
    fallback_threejs = '''
/**
 * 最小Three.js替代版本
 * 僅包含基本功能以確保遊戲能夠運行
 */

// 全局THREE對象
window.THREE = {};

// 基本數學類
THREE.Vector3 = function(x = 0, y = 0, z = 0) {
    this.x = x; this.y = y; this.z = z;
    this.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; return this; };
    this.copy = function(v) { this.x = v.x; this.y = v.y; this.z = v.z; return this; };
    this.clone = function() { return new THREE.Vector3(this.x, this.y, this.z); };
    this.add = function(v) { this.x += v.x; this.y += v.y; this.z += v.z; return this; };
    this.sub = function(v) { this.x -= v.x; this.y -= v.y; this.z -= v.z; return this; };
    this.subVectors = function(a, b) { this.x = a.x - b.x; this.y = a.y - b.y; this.z = a.z - b.z; return this; };
    this.multiplyScalar = function(s) { this.x *= s; this.y *= s; this.z *= s; return this; };
    this.normalize = function() { 
        const l = Math.sqrt(this.x*this.x + this.y*this.y + this.z*this.z); 
        if(l > 0) { this.x /= l; this.y /= l; this.z /= l; } 
        return this; 
    };
    this.length = function() { return Math.sqrt(this.x*this.x + this.y*this.y + this.z*this.z); };
    this.distanceTo = function(v) { 
        const dx = this.x - v.x, dy = this.y - v.y, dz = this.z - v.z; 
        return Math.sqrt(dx*dx + dy*dy + dz*dz); 
    };
    this.applyQuaternion = function(q) { return this; };
    this.applyAxisAngle = function(axis, angle) { return this; };
};

// 四元數
THREE.Quaternion = function(x = 0, y = 0, z = 0, w = 1) {
    this.x = x; this.y = y; this.z = z; this.w = w;
    this.setFromAxisAngle = function(axis, angle) { return this; };
};

// 基本場景
THREE.Scene = function() {
    this.children = [];
    this.background = null;
    this.fog = null;
    this.add = function(object) { this.children.push(object); };
    this.remove = function(object) { 
        const index = this.children.indexOf(object); 
        if(index > -1) this.children.splice(index, 1); 
    };
};

// 基本相機
THREE.PerspectiveCamera = function(fov = 75, aspect = 1, near = 0.1, far = 1000) {
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
    this.quaternion = new THREE.Quaternion();
    this.fov = fov; this.aspect = aspect; this.near = near; this.far = far;
    this.lookAt = function(target) {};
    this.updateProjectionMatrix = function() {};
};

// 基本渲染器
THREE.WebGLRenderer = function(params = {}) {
    this.domElement = params.canvas || document.createElement('canvas');
    this.domElement.width = 800;
    this.domElement.height = 600;
    
    this.setSize = function(width, height) { 
        this.domElement.width = width; 
        this.domElement.height = height; 
    };
    
    this.render = function(scene, camera) {
        const ctx = this.domElement.getContext('2d');
        if (ctx) {
            // 簡單的2D渲染
            ctx.fillStyle = scene.background ? '#87CEEB' : '#000000';
            ctx.fillRect(0, 0, this.domElement.width, this.domElement.height);
            
            // 顯示提示信息
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('3D渲染模式', this.domElement.width/2, this.domElement.height/2 - 20);
            ctx.font = '14px Arial';
            ctx.fillText('使用簡化渲染引擎', this.domElement.width/2, this.domElement.height/2 + 10);
        }
    };
    
    this.setClearColor = function(color) {};
    this.setPixelRatio = function(ratio) {};
    this.shadowMap = { enabled: false, type: null };
};

// 基本幾何體
THREE.BoxGeometry = function(w=1, h=1, d=1) { this.type = 'BoxGeometry'; };
THREE.SphereGeometry = function(r=1, ws=8, hs=6) { this.type = 'SphereGeometry'; };
THREE.PlaneGeometry = function(w=1, h=1, ws=1, hs=1) { this.type = 'PlaneGeometry'; };
THREE.CylinderGeometry = function(rt=1, rb=1, h=1, rs=8) { this.type = 'CylinderGeometry'; };
THREE.ConeGeometry = function(r=1, h=1, rs=8) { this.type = 'ConeGeometry'; };

// 基本材質
THREE.MeshBasicMaterial = function(params = {}) { 
    this.color = params.color || 0xffffff; 
    this.transparent = params.transparent || false;
    this.opacity = params.opacity || 1;
    this.map = params.map || null;
};
THREE.MeshLambertMaterial = function(params = {}) { 
    this.color = params.color || 0xffffff; 
};
THREE.MeshPhongMaterial = function(params = {}) { 
    this.color = params.color || 0xffffff; 
    this.shininess = params.shininess || 30;
};

// 基本網格
THREE.Mesh = function(geometry, material) {
    this.geometry = geometry; 
    this.material = material;
    this.position = new THREE.Vector3(); 
    this.rotation = new THREE.Vector3(); 
    this.scale = new THREE.Vector3(1,1,1);
    this.quaternion = new THREE.Quaternion();
    this.add = function(object) {}; 
    this.remove = function(object) {};
    this.lookAt = function(target) {};
};

// 基本組
THREE.Group = function() {
    this.children = []; 
    this.position = new THREE.Vector3(); 
    this.rotation = new THREE.Vector3(); 
    this.scale = new THREE.Vector3(1,1,1);
    this.add = function(object) { this.children.push(object); }; 
    this.remove = function(object) { 
        const i = this.children.indexOf(object); 
        if(i > -1) this.children.splice(i, 1); 
    };
};

// 基本光照
THREE.AmbientLight = function(color = 0xffffff, intensity = 1) { 
    this.color = color; 
    this.intensity = intensity; 
};
THREE.DirectionalLight = function(color = 0xffffff, intensity = 1) { 
    this.color = color; 
    this.intensity = intensity; 
    this.position = new THREE.Vector3(); 
    this.castShadow = false;
    this.shadow = { mapSize: { width: 1024, height: 1024 } };
};

// 基本霧
THREE.Fog = function(color, near, far) { 
    this.color = color; 
    this.near = near; 
    this.far = far; 
};

// 基本顏色
THREE.Color = function(color) { 
    this.r = 1; this.g = 1; this.b = 1;
    this.setHex = function(hex) { 
        this.r = ((hex >> 16) & 255) / 255;
        this.g = ((hex >> 8) & 255) / 255;
        this.b = (hex & 255) / 255;
        return this; 
    };
    this.setHSL = function(h, s, l) { return this; };
    if (typeof color === 'number') this.setHex(color);
};

// 基本時鐘
THREE.Clock = function() {
    this.startTime = Date.now();
    this.oldTime = this.startTime;
    this.getDelta = function() { 
        const now = Date.now(); 
        const delta = (now - this.oldTime) / 1000; 
        this.oldTime = now;
        return Math.min(delta, 0.1); 
    };
    this.getElapsedTime = function() {
        return (Date.now() - this.startTime) / 1000;
    };
};

// 常量
THREE.REVISION = '149-fallback';
THREE.PCFSoftShadowMap = 1;

console.log('📦 Three.js替代版本已載入 (版本: ' + THREE.REVISION + ')');
console.log('⚠️ 這是簡化版本，提供基本3D功能');
'''
    
    # 保存替代版本
    with open("js/three.min.js", "w", encoding="utf-8") as f:
        f.write(fallback_threejs)
    
    print("✅ Three.js替代版本已創建")
    return True

def main():
    print("🔧 Three.js載入問題修復工具")
    print("=" * 40)
    
    # 備份舊文件
    if os.path.exists("js/three.min.js"):
        try:
            shutil.copy("js/three.min.js", "js/three.min.js.backup")
            print("📁 已備份舊文件")
        except:
            pass
    
    # 嘗試下載正確版本
    print("\n📥 嘗試下載正確的Three.js...")
    if download_threejs_correct():
        print("\n🎉 Three.js下載成功！")
    else:
        print("\n⚠️ 下載失敗，使用替代版本...")
        create_fallback_threejs()
    
    print("\n✅ 修復完成！")
    print("\n🎮 現在可以嘗試以下遊戲版本:")
    print("1. index.html - 原版3D遊戲")
    print("2. game_3d_basic.html - 基本3D版本")
    print("3. game_offline.html - 離線版本（推薦）")

if __name__ == "__main__":
    main()
