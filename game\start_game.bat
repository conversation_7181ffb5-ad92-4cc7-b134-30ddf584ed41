@echo off
chcp 65001 >nul
title 末日生存射擊遊戲服務器

echo.
echo ========================================
echo 🎮 末日生存射擊遊戲服務器
echo ========================================
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python
    echo 💡 請先安裝Python: https://python.org
    echo.
    pause
    exit /b 1
)

REM 檢查必要文件
if not exist "index.html" (
    echo ❌ 未找到 index.html
    echo 💡 請確保在遊戲目錄中運行此腳本
    pause
    exit /b 1
)

if not exist "js\game.js" (
    echo ❌ 未找到 js\game.js
    echo 💡 請確保遊戲文件完整
    pause
    exit /b 1
)

echo ✅ 文件檢查完成
echo 🚀 啟動遊戲服務器...
echo.

REM 啟動Python服務器
python start_server.py

echo.
echo 🛑 服務器已停止
pause
