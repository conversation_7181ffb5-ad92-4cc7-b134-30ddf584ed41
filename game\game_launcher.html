<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 末日生存射擊遊戲 - 啟動器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .launcher {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .title {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 18px;
            margin-bottom: 40px;
            color: #ccc;
        }

        .game-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .game-option {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .game-option:hover {
            transform: translateY(-5px);
            border-color: #4ecdc4;
            box-shadow: 0 10px 30px rgba(78, 205, 196, 0.3);
        }

        .game-option.recommended {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }

        .game-option.recommended::before {
            content: '推薦';
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff6b6b;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }

        .option-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .option-title {
            font-size: 24px;
            margin-bottom: 10px;
            color: #fff;
        }

        .option-description {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .option-features {
            list-style: none;
            text-align: left;
            margin-bottom: 20px;
        }

        .option-features li {
            padding: 5px 0;
            font-size: 13px;
            color: #aaa;
        }

        .option-features li::before {
            content: '✓ ';
            color: #4ecdc4;
            font-weight: bold;
        }

        .launch-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .launch-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
        }

        .status {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            font-size: 14px;
        }

        .status-item {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-good {
            color: #4ecdc4;
        }

        .status-warning {
            color: #ffa726;
        }

        .status-error {
            color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 36px;
            }
            
            .game-options {
                grid-template-columns: 1fr;
            }
            
            .launcher {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="launcher">
        <h1 class="title">🧟 末日生存射擊</h1>
        <p class="subtitle">選擇遊戲版本開始你的生存之旅</p>
        
        <div class="game-options">
            <!-- 離線版 (推薦) -->
            <div class="game-option recommended" onclick="launchGame('offline')">
                <div class="option-icon">📱</div>
                <h3 class="option-title">離線版 (2D)</h3>
                <p class="option-description">完全離線運行，保證流暢體驗</p>
                <ul class="option-features">
                    <li>無需網絡連接</li>
                    <li>流暢的2D圖形</li>
                    <li>完整遊戲機制</li>
                    <li>4種武器系統</li>
                    <li>智能喪屍AI</li>
                </ul>
                <button class="launch-btn">🚀 開始遊戲</button>
            </div>
            
            <!-- 3D Three.js版 -->
            <div class="game-option" onclick="launchGame('3d')">
                <div class="option-icon">🎮</div>
                <h3 class="option-title">3D版 (Three.js)</h3>
                <p class="option-description">使用Three.js的完整3D體驗</p>
                <ul class="option-features">
                    <li>Three.js 3D渲染</li>
                    <li>完整瞄準鏡系統</li>
                    <li>WebGL加速</li>
                    <li>真實3D環境</li>
                    <li>第一人稱視角</li>
                </ul>
                <button class="launch-btn">🎯 體驗3D</button>
            </div>
        </div>
        
        <div class="status">
            <h4>🔍 系統狀態檢查</h4>
            <div class="status-item">
                <span>服務器狀態:</span>
                <span id="serverStatus" class="status-good">✅ 運行中</span>
            </div>
            <div class="status-item">
                <span>Three.js庫:</span>
                <span id="threejsStatus" class="status-good">✅ 已下載 (613KB)</span>
            </div>
            <div class="status-item">
                <span>瀏覽器兼容:</span>
                <span id="browserStatus" class="status-good">✅ 支援</span>
            </div>
            <div class="status-item">
                <span>推薦版本:</span>
                <span class="status-good">📱 離線版 (最穩定)</span>
            </div>
        </div>
    </div>

    <script>
        // 檢查系統狀態
        function checkSystemStatus() {
            // 檢查瀏覽器
            const userAgent = navigator.userAgent;
            const browserStatus = document.getElementById('browserStatus');
            
            if (userAgent.includes('Chrome') || userAgent.includes('Firefox') || userAgent.includes('Edge')) {
                browserStatus.textContent = '✅ 支援';
                browserStatus.className = 'status-good';
            } else {
                browserStatus.textContent = '⚠️ 建議使用Chrome';
                browserStatus.className = 'status-warning';
            }
            
            // 檢查WebGL支援
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (!gl) {
                    const threejsStatus = document.getElementById('threejsStatus');
                    threejsStatus.textContent = '⚠️ WebGL不支援';
                    threejsStatus.className = 'status-warning';
                }
            } catch (e) {
                console.log('WebGL檢查失敗');
            }
        }
        
        // 啟動遊戲
        function launchGame(version) {
            let url;
            
            switch(version) {
                case 'offline':
                    url = 'game_offline.html';
                    console.log('🎮 啟動離線版遊戲');
                    break;
                case '3d':
                    url = 'index.html';
                    console.log('🎮 啟動3D Three.js版遊戲');
                    break;
                default:
                    url = 'game_offline.html';
            }
            
            // 在新標籤頁打開遊戲
            window.open(url, '_blank');
        }
        
        // 頁面載入時檢查狀態
        window.addEventListener('load', checkSystemStatus);
        
        // 鍵盤快捷鍵
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    launchGame('offline');
                    break;
                case '2':
                    launchGame('3d');
                    break;
            }
        });
    </script>
</body>
</html>
