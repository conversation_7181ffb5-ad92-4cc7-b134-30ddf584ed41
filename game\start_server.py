#!/usr/bin/env python3
"""
簡單的HTTP服務器啟動腳本
用於運行末日生存射擊遊戲
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# 設置端口
PORT = 8000

# 確保在正確的目錄中
game_dir = Path(__file__).parent
os.chdir(game_dir)

# 檢查必要文件是否存在
required_files = ['index.html', 'js/game.js']
missing_files = []

for file in required_files:
    if not Path(file).exists():
        missing_files.append(file)

if missing_files:
    print("❌ 缺少必要文件:")
    for file in missing_files:
        print(f"   - {file}")
    print("\n請確保所有遊戲文件都在正確位置。")
    sys.exit(1)

print("🎮 末日生存射擊遊戲服務器")
print("=" * 40)
print(f"📁 遊戲目錄: {game_dir}")
print(f"🌐 端口: {PORT}")
print(f"🔗 遊戲連結: http://localhost:{PORT}")
print("=" * 40)

# 創建HTTP服務器
class GameHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS頭部以避免跨域問題
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        # 自定義日誌格式
        print(f"🌐 {self.address_string()} - {format % args}")

try:
    with socketserver.TCPServer(("", PORT), GameHTTPRequestHandler) as httpd:
        print(f"✅ 服務器啟動成功！")
        print(f"🎮 在瀏覽器中打開: http://localhost:{PORT}")
        print("📝 按 Ctrl+C 停止服務器")
        print()
        
        # 自動打開瀏覽器
        try:
            webbrowser.open(f'http://localhost:{PORT}')
            print("🌐 已自動打開瀏覽器")
        except:
            print("⚠️ 無法自動打開瀏覽器，請手動訪問上述連結")
        
        print()
        httpd.serve_forever()
        
except KeyboardInterrupt:
    print("\n🛑 服務器已停止")
except OSError as e:
    if e.errno == 48:  # Address already in use
        print(f"❌ 端口 {PORT} 已被占用")
        print(f"💡 請嘗試使用其他端口或關閉占用該端口的程序")
        print(f"🔧 或者運行: python start_server.py --port 8001")
    else:
        print(f"❌ 服務器啟動失敗: {e}")
except Exception as e:
    print(f"❌ 未知錯誤: {e}")
