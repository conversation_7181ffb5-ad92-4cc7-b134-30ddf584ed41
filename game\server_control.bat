@echo off
chcp 65001 >nul
title 🎮 遊戲服務器控制台

:menu
cls
echo.
echo ========================================
echo 🎮 末日生存射擊遊戲 - 服務器控制台
echo ========================================
echo.

REM 檢查服務器狀態
echo 🔍 檢查服務器狀態...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -Method Head -TimeoutSec 3; Write-Output '✅ 服務器運行中 (端口 8000)' } catch { Write-Output '❌ 服務器未運行' }" 2>nul

echo.
echo 📋 可用選項:
echo.
echo 1. 🚀 啟動服務器
echo 2. 🛑 停止服務器  
echo 3. 🔄 重啟服務器
echo 4. 🌐 打開遊戲 (原版)
echo 5. 📱 打開遊戲 (離線版)
echo 6. 📊 查看服務器日誌
echo 7. ❌ 退出
echo.

set /p choice="請選擇操作 (1-7): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto stop_server
if "%choice%"=="3" goto restart_server
if "%choice%"=="4" goto open_game
if "%choice%"=="5" goto open_offline
if "%choice%"=="6" goto show_logs
if "%choice%"=="7" goto exit
goto menu

:start_server
echo.
echo 🚀 啟動服務器...
start /B python -m http.server 8000
timeout /t 3 >nul
echo ✅ 服務器啟動命令已執行
echo 🌐 遊戲地址: http://localhost:8000
pause
goto menu

:stop_server
echo.
echo 🛑 停止服務器...
taskkill /f /im python.exe 2>nul
echo ✅ 服務器停止命令已執行
pause
goto menu

:restart_server
echo.
echo 🔄 重啟服務器...
echo 🛑 停止現有服務器...
taskkill /f /im python.exe 2>nul
timeout /t 2 >nul
echo 🚀 啟動新服務器...
start /B python -m http.server 8000
timeout /t 3 >nul
echo ✅ 服務器重啟完成
echo 🌐 遊戲地址: http://localhost:8000
pause
goto menu

:open_game
echo.
echo 🌐 打開原版遊戲...
start http://localhost:8000
echo ✅ 遊戲已在瀏覽器中打開
pause
goto menu

:open_offline
echo.
echo 📱 打開離線版遊戲...
start http://localhost:8000/game_offline.html
echo ✅ 離線版遊戲已在瀏覽器中打開
pause
goto menu

:show_logs
echo.
echo 📊 服務器信息:
echo.
echo 🌐 服務器地址: http://localhost:8000
echo 📁 遊戲目錄: %CD%
echo 🎮 可用遊戲:
echo    • http://localhost:8000 (原版3D)
echo    • http://localhost:8000/game_offline.html (離線2D)
echo.
echo 🔍 檢查端口占用:
netstat -an | findstr :8000
echo.
pause
goto menu

:exit
echo.
echo 👋 感謝使用！
echo.
set /p stop_choice="是否停止服務器？(y/N): "
if /i "%stop_choice%"=="y" (
    echo 🛑 停止服務器...
    taskkill /f /im python.exe 2>nul
    echo ✅ 服務器已停止
)
echo.
echo 🎮 遊戲文件仍在目錄中，隨時可以重新啟動
exit /b 0
