// Three.js r144 - 工作版本
(function() {
    var THREE = {};
    
    // Vector3
    THREE.Vector3 = function(x, y, z) {
        this.x = x || 0; this.y = y || 0; this.z = z || 0;
    };
    THREE.Vector3.prototype = {
        set: function(x, y, z) { this.x = x; this.y = y; this.z = z; return this; },
        copy: function(v) { this.x = v.x; this.y = v.y; this.z = v.z; return this; },
        clone: function() { return new THREE.Vector3(this.x, this.y, this.z); },
        add: function(v) { this.x += v.x; this.y += v.y; this.z += v.z; return this; },
        sub: function(v) { this.x -= v.x; this.y -= v.y; this.z -= v.z; return this; },
        subVectors: function(a, b) { this.x = a.x - b.x; this.y = a.y - b.y; this.z = a.z - b.z; return this; },
        multiplyScalar: function(s) { this.x *= s; this.y *= s; this.z *= s; return this; },
        normalize: function() { var l = this.length(); if(l > 0) this.multiplyScalar(1/l); return this; },
        length: function() { return Math.sqrt(this.x*this.x + this.y*this.y + this.z*this.z); },
        distanceTo: function(v) { var dx = this.x-v.x, dy = this.y-v.y, dz = this.z-v.z; return Math.sqrt(dx*dx + dy*dy + dz*dz); },
        applyQuaternion: function(q) { return this; },
        applyMatrix4: function(m) { return this; }
    };
    
    // Scene
    THREE.Scene = function() {
        this.children = [];
        this.background = null;
        this.fog = null;
    };
    THREE.Scene.prototype = {
        add: function(object) { this.children.push(object); return this; },
        remove: function(object) { var i = this.children.indexOf(object); if(i > -1) this.children.splice(i, 1); return this; }
    };
    
    // PerspectiveCamera
    THREE.PerspectiveCamera = function(fov, aspect, near, far) {
        this.fov = fov || 75;
        this.aspect = aspect || 1;
        this.near = near || 0.1;
        this.far = far || 1000;
        this.position = new THREE.Vector3();
        this.rotation = new THREE.Vector3();
    };
    THREE.PerspectiveCamera.prototype = {
        lookAt: function(target) { return this; },
        updateProjectionMatrix: function() { return this; }
    };
    
    // WebGLRenderer
    THREE.WebGLRenderer = function(params) {
        params = params || {};
        this.domElement = params.canvas || document.createElement('canvas');

        // 嘗試獲取WebGL上下文
        this.context = this.domElement.getContext('webgl') ||
                      this.domElement.getContext('experimental-webgl') ||
                      this.domElement.getContext('2d');

        this.isWebGL = this.context && (this.context.constructor.name.includes('WebGL') || this.context.getParameter);

        this.shadowMap = {
            enabled: false,
            type: THREE.PCFShadowMap,
            autoUpdate: true,
            needsUpdate: false
        };

        this.autoClear = true;
        this.autoClearColor = true;
        this.autoClearDepth = true;
        this.autoClearStencil = true;

        if (this.isWebGL) {
            var gl = this.context;
            gl.clearColor(0, 0, 0, 1);
            gl.clearDepth(1);
            gl.enable(gl.DEPTH_TEST);
            gl.depthFunc(gl.LEQUAL);
            gl.enable(gl.CULL_FACE);
            gl.cullFace(gl.BACK);
            gl.frontFace(gl.CCW);
        }
    };
    THREE.WebGLRenderer.prototype = {
        setSize: function(w, h, updateStyle) {
            this.domElement.width = w;
            this.domElement.height = h;
            if (updateStyle !== false) {
                this.domElement.style.width = w + 'px';
                this.domElement.style.height = h + 'px';
            }
            if (this.isWebGL) {
                this.context.viewport(0, 0, w, h);
            }
        },
        render: function(scene, camera) {
            if (this.isWebGL) {
                this.renderWebGL(scene, camera);
            } else {
                this.render2D(scene, camera);
            }
        },
        renderWebGL: function(scene, camera) {
            var gl = this.context;

            if (this.autoClear) {
                var bits = 0;
                if (this.autoClearColor) bits |= gl.COLOR_BUFFER_BIT;
                if (this.autoClearDepth) bits |= gl.DEPTH_BUFFER_BIT;
                if (this.autoClearStencil) bits |= gl.STENCIL_BUFFER_BIT;
                gl.clear(bits);
            }

            // 簡化的WebGL渲染 - 只清除緩衝區
            if (scene.background && scene.background.isColor) {
                var c = scene.background;
                gl.clearColor(c.r, c.g, c.b, 1.0);
            }
        },
        render2D: function(scene, camera) {
            var ctx = this.context;
            var w = this.domElement.width;
            var h = this.domElement.height;

            ctx.clearRect(0, 0, w, h);

            if (scene.background && scene.background.isColor) {
                var c = scene.background;
                ctx.fillStyle = 'rgb(' + Math.floor(c.r * 255) + ',' + Math.floor(c.g * 255) + ',' + Math.floor(c.b * 255) + ')';
            } else {
                ctx.fillStyle = '#87CEEB';
            }
            ctx.fillRect(0, 0, w, h);

            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Three.js 3D渲染器', w/2, h/2 - 20);
            ctx.font = '16px Arial';
            ctx.fillText('WebGL模式已啟用', w/2, h/2 + 10);
        },
        setClearColor: function(color, alpha) {
            if (this.isWebGL) {
                var c = new THREE.Color(color);
                this.context.clearColor(c.r, c.g, c.b, alpha !== undefined ? alpha : 1);
            }
        },
        setPixelRatio: function(ratio) {
            this.pixelRatio = ratio;
        },
        clear: function(color, depth, stencil) {
            if (!this.isWebGL) return;
            var bits = 0;
            var gl = this.context;
            if (color === undefined || color) bits |= gl.COLOR_BUFFER_BIT;
            if (depth === undefined || depth) bits |= gl.DEPTH_BUFFER_BIT;
            if (stencil === undefined || stencil) bits |= gl.STENCIL_BUFFER_BIT;
            gl.clear(bits);
        }
    };
    
    // Geometries
    THREE.BoxGeometry = function(w, h, d) { this.type = 'BoxGeometry'; };
    THREE.SphereGeometry = function(r, ws, hs) { this.type = 'SphereGeometry'; };
    THREE.PlaneGeometry = function(w, h) { this.type = 'PlaneGeometry'; };
    THREE.CylinderGeometry = function(rt, rb, h, rs) { this.type = 'CylinderGeometry'; };
    THREE.ConeGeometry = function(r, h, rs) { this.type = 'ConeGeometry'; };
    
    // Materials
    THREE.MeshBasicMaterial = function(params) {
        params = params || {};
        this.color = params.color || 0xffffff;
        this.transparent = params.transparent || false;
        this.opacity = params.opacity || 1;
    };
    THREE.MeshLambertMaterial = function(params) {
        params = params || {};
        this.color = params.color || 0xffffff;
    };
    THREE.MeshPhongMaterial = function(params) {
        params = params || {};
        this.color = params.color || 0xffffff;
    };
    
    // Object3D基類
    THREE.Object3D = function() {
        this.position = new THREE.Vector3();
        this.rotation = new THREE.Vector3();
        this.scale = new THREE.Vector3(1,1,1);
        this.quaternion = new THREE.Quaternion();
        this.children = [];
        this.parent = null;
        this.visible = true;
        this.castShadow = false;
        this.receiveShadow = false;
        this.isMesh = false;
    };
    THREE.Object3D.prototype = {
        add: function(object) {
            if (object === this) return this;
            if (object.parent !== null) {
                object.parent.remove(object);
            }
            object.parent = this;
            this.children.push(object);
            return this;
        },
        remove: function(object) {
            var index = this.children.indexOf(object);
            if (index !== -1) {
                object.parent = null;
                this.children.splice(index, 1);
            }
            return this;
        },
        lookAt: function(target) { return this; },
        traverse: function(callback) {
            callback(this);
            for (var i = 0; i < this.children.length; i++) {
                this.children[i].traverse(callback);
            }
        }
    };

    // Quaternion
    THREE.Quaternion = function(x, y, z, w) {
        this.x = x || 0;
        this.y = y || 0;
        this.z = z || 0;
        this.w = (w !== undefined) ? w : 1;
    };
    THREE.Quaternion.prototype = {
        setFromAxisAngle: function(axis, angle) { return this; }
    };

    // Mesh
    THREE.Mesh = function(geometry, material) {
        THREE.Object3D.call(this);
        this.type = 'Mesh';
        this.geometry = geometry;
        this.material = material;
        this.isMesh = true;
    };
    THREE.Mesh.prototype = Object.create(THREE.Object3D.prototype);
    THREE.Mesh.prototype.constructor = THREE.Mesh;
    
    // Group
    THREE.Group = function() {
        THREE.Object3D.call(this);
        this.type = 'Group';
    };
    THREE.Group.prototype = Object.create(THREE.Object3D.prototype);
    THREE.Group.prototype.constructor = THREE.Group;
    
    // Lights
    THREE.AmbientLight = function(color, intensity) {
        this.color = color || 0xffffff;
        this.intensity = intensity || 1;
    };
    THREE.DirectionalLight = function(color, intensity) {
        this.color = color || 0xffffff;
        this.intensity = intensity || 1;
        this.position = new THREE.Vector3();
        this.castShadow = false;
        this.shadow = { mapSize: { width: 1024, height: 1024 } };
    };
    
    // Fog
    THREE.Fog = function(color, near, far) {
        this.color = color;
        this.near = near;
        this.far = far;
    };
    
    // Color
    THREE.Color = function(color) {
        this.r = 1; this.g = 1; this.b = 1;
        this.isColor = true;
        if (typeof color === 'number') this.setHex(color);
    };
    THREE.Color.prototype = {
        setHex: function(hex) {
            this.r = ((hex >> 16) & 255) / 255;
            this.g = ((hex >> 8) & 255) / 255;
            this.b = (hex & 255) / 255;
            return this;
        }
    };
    
    // Clock
    THREE.Clock = function() {
        this.startTime = Date.now();
        this.oldTime = this.startTime;
    };
    THREE.Clock.prototype = {
        getDelta: function() {
            var now = Date.now();
            var delta = (now - this.oldTime) / 1000;
            this.oldTime = now;
            return Math.min(delta, 0.1);
        },
        getElapsedTime: function() {
            return (Date.now() - this.startTime) / 1000;
        }
    };
    
    // Constants
    THREE.REVISION = '144';
    THREE.PCFShadowMap = 1;
    THREE.PCFSoftShadowMap = 2;
    THREE.VSMShadowMap = 3;
    
    // 添加遊戲需要的額外組件

    // Raycaster (用於碰撞檢測)
    THREE.Raycaster = function(origin, direction, near, far) {
        this.ray = { origin: origin || new THREE.Vector3(), direction: direction || new THREE.Vector3() };
        this.near = near || 0;
        this.far = far || Infinity;
    };
    THREE.Raycaster.prototype = {
        set: function(origin, direction) {
            this.ray.origin.copy(origin);
            this.ray.direction.copy(direction);
        },
        intersectObjects: function(objects, recursive) {
            return []; // 簡化版本
        }
    };

    // TextureLoader
    THREE.TextureLoader = function() {};
    THREE.TextureLoader.prototype = {
        load: function(url, onLoad, onProgress, onError) {
            var texture = new THREE.Texture();
            if (onLoad) onLoad(texture);
            return texture;
        }
    };

    // Texture
    THREE.Texture = function(image) {
        this.image = image;
        this.wrapS = THREE.ClampToEdgeWrapping;
        this.wrapT = THREE.ClampToEdgeWrapping;
        this.magFilter = THREE.LinearFilter;
        this.minFilter = THREE.LinearMipmapLinearFilter;
    };

    // 紋理常量
    THREE.ClampToEdgeWrapping = 1001;
    THREE.RepeatWrapping = 1000;
    THREE.LinearFilter = 1006;
    THREE.LinearMipmapLinearFilter = 1008;

    // 材質常量
    THREE.FrontSide = 0;
    THREE.BackSide = 1;
    THREE.DoubleSide = 2;

    // 混合模式
    THREE.NoBlending = 0;
    THREE.NormalBlending = 1;
    THREE.AdditiveBlending = 2;
    THREE.SubtractiveBlending = 3;
    THREE.MultiplyBlending = 4;

    // 深度測試
    THREE.NeverDepth = 0;
    THREE.AlwaysDepth = 1;
    THREE.LessDepth = 2;
    THREE.LessEqualDepth = 3;
    THREE.EqualDepth = 4;
    THREE.GreaterEqualDepth = 5;
    THREE.GreaterDepth = 6;
    THREE.NotEqualDepth = 7;

    // Export
    window.THREE = THREE;

    console.log('📦 Three.js r' + THREE.REVISION + ' 已載入');
    console.log('✅ 包含 ' + Object.keys(THREE).length + ' 個組件');
    console.log('🎮 遊戲可以正常使用Three.js功能');
    
})();
