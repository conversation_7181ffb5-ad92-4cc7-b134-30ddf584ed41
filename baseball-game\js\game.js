document.addEventListener('DOMContentLoaded', () => {
    // 1. 初始化 Canvas
    const canvas = document.getElementById('gameCanvas');
    if (!canvas) {
        console.error('Canvas element with ID "gameCanvas" not found.');
        return;
    }
    const ctx = canvas.getContext('2d');
    canvas.width = 800;
    canvas.height = 600;

    // 2. 定義遊戲變數與物件
    let strikes = 0;
    let outs = 0;

    const ball = {
        x: canvas.width / 2,
        y: 100,
        radius: 15,
        dx: 0, // 初始速度為 0
        dy: 0, // 初始速度為 0
        color: '#FFD700' // 金色
    };

    // 定義投手丘、本壘板和打擊區
    const pitcherMound = { x: canvas.width / 2, y: 100 };
    const homePlate = { y: canvas.height - 100 };
    const strikeZone = {
        x: canvas.width / 2 - 50,
        y: homePlate.y - 100,
        width: 100,
        height: 100
    };

    // 投球函式
    function pitch() {
        ball.x = pitcherMound.x;
        ball.y = pitcherMound.y;
        // 隨機化投球方向，使其更有趣
        ball.dx = (Math.random() - 0.5) * 4;
        ball.dy = 8; // 固定垂直速度
    }

    // 繪製球的函式
    function drawBall() {
        ctx.beginPath();
        ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
        ctx.fillStyle = ball.color;
        ctx.fill();
        ctx.closePath();
    }

    // 繪製計分板
    function drawScore() {
        ctx.font = '24px Arial';
        ctx.fillStyle = '#000';
        ctx.fillText(`Strikes: ${strikes}`, 20, 40);
        ctx.fillText(`Outs: ${outs}`, 20, 70);
    }

    // 3. 建立遊戲迴圈
    function gameLoop() {
        // a. 清除整個 canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // b. 繪製物件
        drawBall();
        drawScore();

        // c. 更新球的座標
        ball.x += ball.dx;
        ball.y += ball.dy;

        // d. 檢查球是否超過本壘板
        if (ball.y > homePlate.y + ball.radius) {
            console.log("Miss!"); // 如果球沒被打到就超過本壘板，算一次好球
            strikes++;
            if (strikes >= 3) {
                outs++;
                strikes = 0;
            }
            pitch(); // 重置球的位置並準備下一次投球
        }

        // 使用 requestAnimationFrame 來不斷呼叫此函式
        requestAnimationFrame(gameLoop);
    }

    // 4. 處理使用者輸入
    canvas.addEventListener('click', () => {
        // 檢查點擊時球是否在打擊區內
        const inStrikeZone = ball.x > strikeZone.x &&
                             ball.x < strikeZone.x + strikeZone.width &&
                             ball.y > strikeZone.y &&
                             ball.y < strikeZone.y + strikeZone.height;

        if (inStrikeZone) {
            console.log("Hit!");
            // (可選) 擊中後可以重置球或增加分數
            pitch();
        } else {
            console.log("Miss!");
            strikes++;
            if (strikes >= 3) {
                outs++;
                strikes = 0;
            }
        }
    });

    // 啟動遊戲
    pitch(); // 第一次投球
    gameLoop();
});



