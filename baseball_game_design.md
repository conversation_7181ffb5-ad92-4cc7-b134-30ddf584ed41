# 網頁棒球遊戲技術設計文件

這份文件旨在為新的網頁棒球遊戲提供技術藍圖，作為後續開發工作的基礎。

## 1. 核心玩法 (Core Gameplay)

遊戲的核心機制圍繞著投手投球與玩家揮棒的互動。

*   **遊戲流程:**
    1.  遊戲開始後，投手會從投手丘投出棒球。
    2.  球會以一定的速度和軌跡飛向本壘板。
    3.  玩家需要在球進入打擊區的瞬間，點擊滑鼠左鍵來揮棒。
    4.  系統會根據玩家的點擊時機來判斷結果。

*   **球路變化:**
    *   為了增加挑戰性，投手會隨機投出不同類型的球路，例如：
        *   **直線球 (Fastball):** 速度快，路徑相對筆直。
        *   **曲球 (Curveball):** 速度較慢，路徑會帶有明顯的水平或垂直變化。
        *   **變速球 (Changeup):** 外觀像快速球，但實際速度較慢，旨在迷惑玩家。
    *   系統將根據預設的物理參數來計算這些球路的飛行軌跡。

*   **判斷邏輯:**
    *   **擊中 (Hit):** 如果玩家在球進入本壘板上方的「可打擊區域」時點擊，就會被判定為「擊中」。根據點擊的精準度，可以進一步分為安打、全壘打等。
    *   **揮空 (Swing and Miss):** 如果玩家在球進入「可打擊區域」之前或之後點擊，或者在球的飛行路徑上但時機不對，則判定為「揮空」。
    *   **好球 (Strike):** 如果球進入「可打擊區域」但玩家沒有揮棒，則計為一次「好球」。
    *   **壞球 (Ball):** 如果球未進入「可打擊區域」且玩家沒有揮棒，則計為一次「壞球」。

## 2. 技術棧 (Tech Stack)

為了保持專案的輕量與標準化，我們將採用以下技術，不依賴任何外部函式庫或框架。

*   **前端語言:**
    *   **HTML:** 用於建立遊戲頁面的基本結構。
    *   **CSS:** 用於設計遊戲畫面的樣式，包括場景、角色和 UI。
    *   **JavaScript (ES6+):** 用於實現所有遊戲邏輯，包括遊戲迴圈、物理模擬、使用者輸入處理和狀態管理。

## 3. 檔案結構 (File Structure)

專案將採用以下目錄結構，以確保程式碼的組織性和可維護性。

```
/baseball-game
|
|-- index.html             # 遊戲主頁面，包含 Canvas 元素
|
|-- /css
|   |-- style.css          # 主要樣式表
|
|-- /js
|   |-- game.js            # 核心遊戲邏輯、迴圈和狀態管理
|
|-- /assets/
    |-- /images/           # 存放遊戲圖片 (例如：球場、球員、球)
    |-- /sounds/           # 存放遊戲音效 (例如：揮棒聲、擊球聲、群眾歡呼聲)
```

## 4. 遊戲迴圈 (Game Loop)

遊戲的動畫和狀態更新將由一個持續運行的遊戲迴圈驅動。

*   **實現方式:** 我們將使用 `window.requestAnimationFrame()` 來建立一個高效能、對瀏覽器友善的遊戲迴圈。
*   **每幀更新 (Updates per Frame):**
    1.  **清除畫布 (Clear Canvas):** 在每一幀開始時，清除整個畫布，為繪製新畫面做準備。
    2.  **更新狀態 (Update State):**
        *   更新球的位置（根據其速度和軌跡）。
        *   檢查玩家的輸入（是否點擊滑鼠）。
        *   根據玩家輸入和球的狀態更新遊戲邏輯（例如：判斷是否擊中、揮空）。
    3.  **繪製物件 (Render Objects):**
        *   在畫布上重新繪製所有遊戲物件，包括球場、投手、球、打者和 UI 元素（如分數、好壞球數）。
    4.  **循環呼叫:** 再次呼叫 `requestAnimationFrame()` 以安排下一幀的繪製。
