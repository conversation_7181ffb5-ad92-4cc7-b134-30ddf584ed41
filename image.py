# import cv2

# try:
#     img = cv2.imread("mlb picture.png", cv2.IMREAD_GRAYSCALE)
#     png = cv2.imread('mlb picture.png')
#     img01 = cv2.imread("mlb picture.png", cv2.IMREAD_REDUCED_GRAYSCALE_4)
#     img02 = cv2.imread("mlb picture.png", cv2.IMREAD_COLOR)
#     cv2.imshow('mlb',img)
#     cv2.imshow('picture',png)
#     cv2.imshow('p',img01)
#     cv2.imshow('i',img02)
#     cv2.waitKey(0)
#     cv2.destroyAllWindows()
# except:
#     print('找不到圖片')


# import cv2

# # img = cv2.imread("mlb picture.png", cv2.IMREAD_GRAYSCALE)
# # cv2.imwrite('mlb_picture.png',img,[cv2.IMWRITE_WEBP_QUALITY, 1])
# img = cv2.imread("mlb picture.png")
# img01 = cv2.imread("mlb picture.png",cv2.IMREAD_GRAYSCALE)
# cv2.imwrite("mlb_picture.webp", img)

# import cv2
# import numpy as np

# img = np.zeros((500, 500, 3), dtype='uint8')
# cv2.imshow("test",img)
# cv2.waitKey(0)

# import cv2
# cap = cv2.VideoCapture('https://cctvs.freeway.gov.tw/live-view/mjpg/video.cgi?camera=452')

# if not cap.isOpened():
#     print("Cannot open camera")
#     exit()
# while True:
#     ret, frame = cap.read()             
#     if not ret:
#         print("Cannot receive frame")   
       
#         cap = cv2.VideoCapture('https://cctvs.freeway.gov.tw/live-view/mjpg/video.cgi?camera=452')
#         continue
#     cv2.imshow('oxxostudio', frame)     
#     if cv2.waitKey(1) == ord('q'):     
#         break
# cap.release()                          
# cv2.destroyAllWindows()                 

# import cv2
# cap = cv2.VideoCapture(0)
# width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
# height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
# fourcc = cv2.VideoWriter_fourcc(*'mp4v')
# out = cv2.VideoWriter("output.mp4", fourcc, 30.0, (width, height)) 
# fps = int(cap.get(cv2.CAP_PROP_FPS))
# print(fps)
# if not cap.isOpened():
#     print("Cannot open camera")
#     exit()
# while True:
#     ret, frame = cap.read()            
#     if not ret:
#         print("Cannot receive frame")   
#         break
#     out.write(frame)
#     cv2.imshow('oxxostudio', frame)     
#     if cv2.waitKey(1) == ord('q'):      
#         break
# cap.release()
# out.release()      
# cv2.destroyAllWindows() 

# import cv2
# img = cv2.imread("mlb picture.png")
# print("shape:",img.shape)
# print("size:",img.size)
# print("type:", img.dtype)
# cv2.imshow('mlb picture', img)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2
# import numpy as np
# from PIL import ImageFont,  ImageDraw, Image
# img = np.zeros((300,800,3), dtype='uint8')
# fontpath = 'NotoSansTC-VariableFont_wght.ttf'
# font = ImageFont.truetype(fontpath, 50)
# fontpath1 = 'Cubic_11_1.010_R.ttf'
# font1 = ImageFont.truetype(fontpath1, 50)
# imgpil = Image.fromarray(img)
# draw = ImageDraw.Draw(imgpil)
# draw.text((0,0),"嗨嗨!", fill=(0,225,0), font=font)
# draw.text((0,200),"嗨嗨!", fill=(0,225,0), font=font1)
# img = np.array(imgpil)

# cv2.imshow('chinese', img)
# cv2.waitKey(0)
# cv2.destroyAllWindows
# # cv2.ellipse(img,(150,150),(100,50),45,0,360,(0,0,255),5)
# cv2.ellipse(img,(150,150),(30,100),90,0,360,(255,150,0),5)
# cv2.ellipse(img,(150,150),(20,120),45,0,360,(0,255,255),5)
# cv2.imshow('oxxostudio', img)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
# img = np.zeros((300,300,3), dtype="uint8")
# prs = np.array([[50,100],[100,50],[150,100],[150,100],[100,200]])
# cv2.fillPoly(img,[prs],(0,0,255))   
# cv2.imshow('oxxostudio', img)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
# text = "Dodgaers"
# org =(75,170)
# fontface = cv2.FONT_HERSHEY_SCRIPT_COMPLEX
# fontscale = 5
# color = (180,153,51)
# thiclness = 4
# linetype = cv2.LINE_8
# cv2.putText(img, text, org, fontface, fontscale, color, thiclness, linetype)
# cv2.imshow('oxxostudio', img)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2
# import numpy as np
# img = cv2.imread("mlb picture.png")
# x = 100
# y = 100
# w = 300 
# h = 200
# crop_img = img [y:y+h, x:x+w]
# cv2.imshow("Original", img)
# cv2.imshow("Crop", crop_img)
# output = np.zeros((500, 1000, 3), dtype="uint8")
# output[0:250, 0:500] = (255, 20, 50)
# output[251:500, 0:500] = (0, 0, 180)
# output[0:250, 501:1000] = (50, 165, 20)
# output[251:500, 501:1000] = (255, 255, 255)
# output [y+20:y+h+20, x+250:x+w+250] = crop_img
# output1 = 255-output
# img1 = 255-img
# cv2.imshow("Output", output)
# cv2.imshow("Output1", output1)
# cv2.imshow("img1", img1)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2
# import numpy as np
# img = cv2.imread("mlb picture.png")
# contrast = 1000
# brightness = 0
# output = img * (contrast/127 + 1) - contrast + brightness 

# output = np.clip(output, 0, 255)
# output = np.uint8(output)

# cv2.imshow('oxxostudio1', img)    
# cv2.imshow('oxxostudio2', output) 
# cv2.waitKey(0)                    
# cv2.destroyAllWindows()

# import cv2
# img = cv2.imread('12強.jpg')
# output1 = cv2.blur(img, (5, 5))   
# output2 = cv2.blur(img, (5, 50))
# output3 = cv2.GaussianBlur(img, (5, 5), 0)
# output4 = cv2.GaussianBlur(img, (55, 55), 0)
# output5 = cv2.medianBlur(img, 5)   
# output6 = cv2.medianBlur(img, 25)
# output7 = cv2.bilateralFilter(img, 50, 0, 0)
# output8 = cv2.bilateralFilter(img, 50, 50, 100)
# output9 = cv2.bilateralFilter(img, 50, 100, 1000)
# cv2.waitKey(0)  
# cv2.imshow("正常", img)
# cv2.imshow("5x5", output1)
# cv2.imshow("5x50", output2)
# cv2.imshow("G 5x5", output3)
# cv2.imshow("G 25x25", output4)
# cv2.imshow('M 5', output5)
# cv2.imshow('M 25', output6)
# cv2.imshow('f1', output7)
# cv2.imshow('f2', output8)
# cv2.imshow('f3', output9)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2
# import numpy as np

# img = np.zeros((300,300,3), dtype='uint8')
# cv2.circle(img,(150,100),100,(0,0,255),-1)  
# cv2.imwrite("test_red.png", img)
# cv2.imshow("red", img)

# img = np.zeros((300,300,3), dtype='uint8')
# cv2.circle(img,(100,200),100,(0,225,0),-1)  
# cv2.imwrite("test_green.png", img)
# cv2.imshow("green", img)

# img = np.zeros((300,300,3), dtype='uint8')
# cv2.circle(img,(200,200),100,(255,0,0),-1)  
# cv2.imwrite("test_blue.png", img)
# cv2.imshow("blue", img)

# img_red = cv2.imread('test_red.png')
# img_blue = cv2.imread('test_blue.png')
# img_green = cv2.imread('test_green.png')

# output = cv2.add(img_blue, img_green)
# cv2.imshow("red_green",output)
# output = cv2.add(output, img_red)
# cv2.imshow("red_green_blue",output)

# cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread("12.jpg")
# logo = cv2.imread("12logo.png")
# logo = cv2.resize(logo, (img.shape[1], img.shape[0],))

# output = cv2.addWeighted(img, 0.4, logo, 0.5, 50)

# cv2.imshow("addweight", output)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2

# img = cv2.imread("gradient.png")
# img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
# ret, output1 = cv2.threshold(img_gray, 127.5, 255, cv2.THRESH_BINARY)
# ret, output2 = cv2.threshold(img_gray, 127.5, 255, cv2.THRESH_BINARY_INV)
# ret, output3 = cv2.threshold(img_gray, 127.5, 255, cv2.THRESH_TRUNC)      
# ret, output4 = cv2.threshold(img_gray, 127.5, 255, cv2.THRESH_TOZERO)     
# ret, output5 = cv2.threshold(img_gray, 127.5, 255, cv2.THRESH_TOZERO_INV) 
# cv2.imshow("THRESH_BINARY", output1)
# cv2.imshow("THRESH_BINARY_INV", output2)
# cv2.imshow("THRESH_TRUNC", output3)
# cv2.imshow("THRESH_TOZERO", output4)
# cv2.imshow("THRESH_TOZERO_INV", output5)
# cv2.imshow("original", img)

# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2

# img = cv2.imread("test.jpeg")
# img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
# ret, output1 = cv2.threshold(img_gray, 127.5, 255, cv2.THRESH_BINARY)
# output2 = cv2.adaptiveThreshold(img_gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2)
# output3 = cv2.adaptiveThreshold(img_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 291, 2)

# # img_gray2 = 
# output4 = cv2.adaptiveThreshold(img_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)       
# cv2.imshow("oxxostudio2", output1)
# cv2.imshow("oxxostudio3", output2)
# cv2.imshow("oxxostudio4", output3)
# cv2.imwrite("gaussian.png", output3)
# cv2.imshow("oxxostudio5", output4)
# cv2.imshow("oxxostudio1", img)

# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2

# img = cv2.imread("gaussian.png")
# img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
# output3 = cv2.adaptiveThreshold(img_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 101, 5) 
# img_gray2 = cv2.medianBlur(img_gray, 3)
# output4 = cv2.adaptiveThreshold(img_gray2, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 101, 25) 
# cv2.imshow("gaussiangray2", output4)     
# cv2.imshow("gaussiangray1", output3)
# cv2.imshow("gaussian", img)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2
# img = cv2.imread('tesssssssst.png')
# cv2.imshow('oxxostudio1', img)   

# img2 = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
# kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (11, 11))

# img = cv2.erode(img, kernel)     
# cv2.imshow('oxxostudio2', img)   

# img = cv2.dilate(img, kernel)    
# cv2.imshow('oxxostudio3', img)   

# cv2.waitKey(0)                  
# cv2.destroyAllWindows()

# import cv2
# img = cv2.imread('Mona.png')
# img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  
# img = cv2.medianBlur(img, 5)                 
# output = cv2.Laplacian(img, -1, 1, 5)
# output1 = cv2.Sobel(img, -1, 1, 1, 1, 7) 
# output2 = cv2.Canny(img, 122, 122)       
# cv2.imshow('oxxostudio1', output)
# cv2.imshow('oxxostudio2', output1)
# cv2.imshow('oxxostudio4', output2)
# cv2.imshow('oxxostudio3', img)
# cv2.waitKey(0)                               
# cv2.destroyAllWindows()

# import cv2
# cap = cv2.VideoCapture(0)
# if not cap.isOpened():
#     print("Cannot open camera")
#     exit()
# while True:
#     ret, frame = cap.read()
#     if not ret:
#         print("Cannot receive frame")
#         break
#     img = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)  
#     img = cv2.medianBlur(img, 7)                   
#     img = cv2.Canny(img, 36, 36)                   
#     cv2.imshow('oxxostudio', img)
#     if cv2.waitKey(1) == ord('q'):
#         break                                      
# cap.release()
# cv2.destroyAllWindows()

# import cv2
# import numpy as np

# img = cv2.imread('japan.png')
# def floodFill(source, mask, seedPoint, newVal, loDiff, upDiff, flags=cv2.FLOODFILL_FIXED_RANGE):
#     result = source.copy()
#     cv2.floodFill(result, mask=mask, seedPoint=seedPoint, newVal=newVal, loDiff=loDiff, upDiff=upDiff, flags=flags)
#     return result

# h, w = img.shape[:2]                    
# mask = np.zeros((h+2,w+2,1), np.uint8)  
# output = floodFill(img, mask, (100,10), (0,0,255), (100,100,60), (100,100,100))

# cv2.imshow('oxxostudio', output)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2
# img = cv2.imread("unilions 2008.png")
# size = img.shape
# level = 10
# h = int(size[0]/level)
# w = int(size[1]/level)
# mosaic = cv2.resize(img, (h, h), interpolation = cv2.INTER_LINEAR)
# mosaic = cv2.resize(mosaic, (size[1], size[0]), interpolation = cv2.INTER_NEAREST)
# cv2.imshow("picture", mosaic)
# cv2.imshow("picture1", img)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2
# img = cv2.imread('mona.jpg')

# dot1 = []                          
# dot2 = []                          


# def show_xy(event,x,y,flags,param):
#     global dot1, dot2, img         
#     if flags == 1:
#         if event == 1:
#             dot1 = [x, y]          
#         if event == 0:
#             img2 = img.copy()     
#             dot2 = [x, y]          
            
#             cv2.rectangle(img2, (dot1[0], dot1[1]), (dot2[0], dot2[1]), (0,0,255), 2)
           
#             cv2.imshow('oxxostudio', img2)

# cv2.imshow('oxxostudio', img)
# cv2.setMouseCallback('oxxostudio', show_xy)

# cv2.waitKey(0)   
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread("unilions 2008.png")
# img1 = cv2.imread("unilions 2008.png")
# x = 190
# y = 200
# cw = 180
# ch = 70
# mosaic = img[y:y+ch, x:x+cw]
# level = 10
# h = int(ch/level)
# w = int(cw/level)
# mosaic = cv2.resize(mosaic, (h, w), interpolation = cv2.INTER_LINEAR)
# mosaic = cv2.resize(mosaic, (cw,ch), interpolation = cv2.INTER_NEAREST)
# img[y:y+ch, x:x+cw] = mosaic
# cv2.imshow("picture", img)
# cv2.imshow("picture1", img1)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import cv2
# cap1 = cv2.VideoCapture("https://cctvc.freeway.gov.tw/abs2mjpg/bmjpg?camera=1106")
# cap2 = cv2.VideoCapture("https://cctvp02.freeway.gov.tw/mjpeg/X01001510110801")
         

# if not cap1.isOpened():
#     print("Cannot open camera1")
#     exit()
# if not cap2.isOpened():
#     print("Cannot open camera2")
#     exit()

# while True:
#     ret1, img1 = cap1.read()         
#     ret2, img2 = cap2.read()         

#     cv2.imshow('oxxostudio1', img1)  
#     cv2.imshow('oxxostudio2', img2)  
#     if cv2.waitKey(1) == ord('q'):
#         break
# cap1.release()
# cap2.release()
# cv2.destroyAllWindows()

# import cv2
# cap1 = cv2.VideoCapture("https://cctvc.freeway.gov.tw/abs2mjpg/bmjpg?camera=1106")
# cap2 = cv2.VideoCapture("https://cctvp02.freeway.gov.tw/mjpeg/X01001510110801")

# if not cap1.isOpened():
#     print("Cannot open camera1")
#     exit()
# if not cap2.isOpened():
#     print("Cannot open camera2")
#     exit()

# while True:
#     ret1, img1 = cap1.read()
#     ret2, img2 = cap2.read()
#     img1 = cv2.resize(img1 ,(176, 120))
#     img2 = cv2.resize(img2 ,(352,240))
#     img2[120:240,176:352] = img1
#     cv2.rectangle(img2, (174,118), (350,238), (0,0,225), 2)
#     cv2.imshow("Two cam", img2)
#     if cv2.waitKey(1) == ord('q'):
#         break
# cap1.release()
# cap2.release()
# cv2.destroyAllWindows()

# import cv2
# img = cv2.imread("unilions2008.png")

# def show_xy(event, x, y, flags, pram):
#     print(event, x, y, flags, pram)

# cv2.imshow("Original", img)
# cv2.setMouseCallback("Original", show_xy)

# cv2.waitKey(0) 
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread("unilions 2008.png")

# def show_xy(event, x, y, flags, pram):
#     if event ==0:
#        img2 = img.copy()
#        cv2.circle(img2, (x, y), 20, (0, 0, 225), 2)
#        cv2.imshow("unilions 2008", img2)
#     if event == 1:
#         color = img[y, x]
#     print(color)

# cv2.imshow("unilions 2008", img)
# cv2.setMouseCallback("unilions 2008", show_xy)

# cv2.waitKey(0) 
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread("unilions2008.png")

# def show_xy(event, x, y, flags, pram):
#     if event ==0:
#        img2 = img.copy()
#        cv2.circle(img2, (x, y), 20, (0, 0, 225), 2)
#        cv2.imshow("unilions2008", img2)
#     if event == 1:
#         color = img[y, x]
#     print(color)

# cv2.imshow("unilions2008", img)
# cv2.setMouseCallback("unilions2008", show_xy)

# cv2.waitKey(0) 
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread("unilions2008.png")

# dots = []
# def show_xy(event, x, y, flags, pram):
#     if event ==1:
#         dots.append([x,y])
#         cv2.circle(img, (x, y), 5, (0, 0, 0), -1)
#         num = len(dots)
#         if num > 1:
#             x1 = dots[num-2][0]
#             y1 = dots[num-2][1]
#             x2 = dots[num-1][0]
#             y2 = dots[num-1][1]
#             cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), 2)
#         cv2.imshow("unilions2008", img)
# cv2.imshow("unilions2008", img)   
# cv2.setMouseCallback("unilions2008", show_xy)

# cv2.waitKey(0) 
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread("unilions2008.png")

# dot1 = []
# dot2 = []

# def show_xy(event, x, y, flags, pram):
#     global dot1, dot2, img, img2
#     if flags == 1:
#         if event == 1:
#             dot1 = [x, y]
#         if event == 0:
#             dot2 = [x, y]
#             img2 = img.copy()
#             cv2.rectangle(img2, (dot1[0], dot1[1]), (dot2[0], dot2[1]), (0, 0, 0), 2)
#             cv2.imshow("unilions2008", img2)
#         if event == 4:
#             level =8
#             h = int((dot2[0]-dot1[0]) / level)
#             w = int((dot2[1]-dot1[1]) / level)
#             mosaic = img[dot1[1]:dot2[1], dot1[0]:dot2[0]]
#             mosaic =cv2.resize(mosaic, (w,h), interpolation=cv2.INTER_LINEAR)
#             mosaic =cv2.resize(mosaic, (dot2[0]-dot1[0], dot2[1]-dot1[1]), interpolation=cv2.INTER_NEAREST)
#             img2[dot1[1]:dot2[1], dot1[0]:dot2[0]] = mosaic
#             img = img2
#             cv2.imshow("unilions2008", img2)

# cv2.imshow("unilions2008", img)   

# cv2.setMouseCallback("unilions2008", show_xy)

# cv2.waitKey(0) 
# cv2.destroyAllWindows()
# import cv2
# img = cv2.imread('unilions2008.png')
# dot1 = []
# dot2 = []
# def show_xy(event,x,y,flags,param):
#     global dot1, dot2, img, img2
#     if flags == 1:
#         if event == 1:
#             dot1 = [x, y]
#         if event == 0:
#             img2 = img.copy()
#             dot2 = [x, y]
#             cv2.rectangle(img2, (dot1[0], dot1[1]), (dot2[0], dot2[1]), (0,0,255), 2)
#             cv2.imshow('unilions2008', img2)
#     if event == 4:
#         print(event)
#         level =8
#         h = int((dot2[0]-dot1[0]) / level)
#         w = int((dot2[1]-dot1[1]) / level)
#         mosaic = img[dot1[1]:dot2[1], dot1[0]:dot2[0]]
#         mosaic =cv2.resize(mosaic, (w,h), interpolation=cv2.INTER_LINEAR)
#         mosaic =cv2.resize(mosaic, (dot2[0]-dot1[0], dot2[1]-dot1[1]), interpolation=cv2.INTER_NEAREST)
#         img2[dot1[1]:dot2[1], dot1[0]:dot2[0]] = mosaic
#         img = img2
#         cv2.imshow("unilions2008", img2)

# cv2.imshow('unilions2008', img)
# cv2.setMouseCallback('unilions2008', show_xy)

# cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2
# import numpy as np

# dots = []
# w = 420
# h = 240
# draw = np.zeros((h,w,3), dtype="uint8")
# def show_xy(event,x,y,flags,param):
#     global dots, draw
#     if flags == 1:
#         if event == 1:
#             dots.append([x,y])
#         if event == 4:
#             dots = []
#         if event == 0 or event == 4:
#             dots.append([x,y])
#             x1 = dots[len(dots)-2][0]    
#             y1 = dots[len(dots)-2][1]    
#             x2 = dots[len(dots)-1][0]     
#             y2 = dots[len(dots)-1][1]     
#             cv2.line(draw,(x1,y1),(x2,y2),(0,225,0,255),2) 
#         cv2.imshow('oxxostudio', draw)

# cv2.imshow('oxxostudio', draw)
# cv2.setMouseCallback('oxxostudio', show_xy)

# while True:
#     Keyboard = cv2.waitKey(5)
#     if Keyboard == ord("q"):
#         break
#     if Keyboard == ord("r"):
#         draw = np.zeros((h,w,4), dtype='uint8')  
#         cv2.imshow('oxxostudio', draw)

# cv2.destroyAllWindows()
# import cv2

# cv2.namedWindow("ASCII")

# while True:
#     keycode = cv2.waitKey(0)
#     c= chr(keycode)
#     print(c, keycode)
#     if keycode == 27:
#         break

# cv2.destroyAllWindows()
# import cv2

# img = cv2.imread("12.jpg")
# cv2.imshow("Tracker", img)

# def test(val):
#     print(val)

# cv2.createTrackbar("test", "Tracker", 0, 225 , test)
# cv2.setTrackbarPos("test", "Tracker", 100)

# keycode = cv2.waitKey(0)
# cv2.destroyAllWindows()
# import cv2
# import numpy as np

# img = cv2.imread("12.jpg")

# contrast = 0
# brightness = 0
# cv2.imshow("Tracker", img)

# def adjust(i,c,b):
#     output = i * (c/100 + 1) - c + b
#     output = np.clip(output, 0, 255)
#     output = np.uint8(output)
#     cv2.imshow("Tracker", output)

# def brightness_fn(val):
#     global img, contrast, brightness
#     brightness = val - 100
#     adjust(img, contrast, brightness)

# def contrast_fn(val):
#     global img, contrast, brightness
#     contrast = val - 100
#     adjust(img, contrast, brightness)

# cv2.createTrackbar("brightness", "Tracker", 0, 200, brightness_fn)
# cv2.setTrackbarPos("brightness", "Tracker", 100)
# cv2.createTrackbar("contrast", "Tracker", 0, 200, brightness_fn)
# cv2.setTrackbarPos("contrast", "Tracker", 100)

# keycode = cv2.waitKey(0)
# cv2.destroyAllWindows()
# import tkinter as tk
# root = tk.Tk()
# root.title("test")
# root.iconbitmap("logo1.ico")

# root.minsize(200,200)
# root.maxsize(1000,1000)
# # root.resizable(False,False)
# # root.configure(background = "#7A378B")
# root.configure(background ="#5d0041")

# window_width = root.winfo_screenwidth()    
# window_height = root.winfo_screenheight()  
# w = 640
# h = 480
# l = int((window_width - w)/2)
# t = int((window_height - h)/2)
# root.geometry(f"{w}x{h}+{l}+{t}")  #root.geometry("680*240+50+100")
# root.mainloop()

# import tkinter as tk
# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")
# my_label = tk.Label(root, 
#                     text="unilions", 
#                     font = ("Arial", 40, "bold"), 
#                     fg = "orange", 
#                     bg = "black", 
#                     bitmap = "info", 
#                     compound = "center", 
#                     relief = "groove", 
#                     padx = 100, 
#                     pady = 100, 
#                     cursor = "plus")
                    
# my_label.pack()

# root.mainloop()
# import tkinter as tk

# root = tk.Tk()
# root.title('oxxo.studio')
# root.geometry('200x200')

# btn = tk.Button(root, text='我是按鈕')     
# btn.pack()                                

# root.mainloop()
# import tkinter as tk
# import time
# root = tk.Tk()
# root.title("text")
# root.geometry("1000x100+50+50")

# num = 0
# text = tk.StringVar()
# text.set(num)         

# def add():
#     global num   
#     num += 10000     
#     text.set(num)       

# mylabel = tk.Label(root, 
#                    textvariable= text, 
#                    font=('Arial',20))  
# mylabel.pack()

# btn = tk.Button(root, 
#                 text = "button", 
#                 font = ('Arial', 30, 'bold'),
#                 padx = 100, 
#                 pady = 100, 
#                 command = add)
# btn.pack()

# for i in range(10):
#     btn.invoke()
#     btn.update()
#     time.sleep(1)

# root.mainloop()
# import tkinter as tk
# root = tk.Tk()
# root.title("text")
# root.geometry("1000x1000+250+250")

# result = tk.StringVar()
# result.set("")
# lable_result = tk.Label(root, textvariable = result, 
#                         font = ("Arial", 20, "bold"), fg = "#ff9900")

# lable_result.pack()

# def show():
#     print(val1.get(), val2.get())
#     result.set(val1.get() + " " + val2.get()) 
    

# val1 = tk.StringVar()
# check_btn1 = tk.Checkbutton(root, text = "選項1", 
#                             variable = val1, onvalue = "選項1", offvalue = "--", 
#                             command = show) 
# check_btn1.pack()
# check_btn1.deselect()

# val2 = tk.StringVar()
# check_btn2 = tk.Checkbutton(root, text = "選項2", 
#                             variable = val2, onvalue = "選項2", offvalue = "--", 
#                             command = show)
# check_btn2.pack()
# check_btn2.deselect()

# def click_toggle():
#     check_btn1.toggle()
#     check_btn2.toggle()

# btn1 = tk.Button(root, text = "toggle", command = click_toggle)
# btn1.pack()

# def click_invoke():
#     check_btn1.invoke()
#     check_btn2.invoke()

# btn2 = tk.Button(root, text = "invoke", command = click_invoke)
# btn2.pack()

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# entey = tk.Entry(root, width = 15, show = "💩")
# entey.pack()
# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# a = tk.StringVar()
# a.set("")

# tk.Label(root, textvariable = a).pack()
# tk.Entry(root, textvariable = a).pack()

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# lable_text = tk.StringVar()
# entry_text = tk.StringVar()
# lable_text.set("")

# def show():
#     lable_text.set(entry_text.get())  

# def clear():
#     entry_text.set(" ")
#     entry.delete(0, "end")

# lable = tk.Label(root, textvariable = lable_text)
# lable.pack()
# entry = tk.Entry(root, textvariable = entry_text)
# entry.pack()

# btn1 = tk.Button(root, text = "顯示", command=show)
# btn1.pack()
# btn2 = tk.Button(root, text = "清除", command=clear)
# btn2.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# text = tk.Text(root, width = 5, height = 20)
# text.pack()

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("500x300+50+100")

# lable_text = tk.StringVar()
# lable_text.set("")

# lable = tk.Label(root, textvariable = lable_text)
# lable.pack()

# text = tk.Text(root, height=8, width=8)
# text.pack()
# def show():
#     lable_text.set(text.get(1.0, "end-1c"))  

# def clear():
#     lable_text.set(" ")
#     text.delete(1.0, "end")

# btn1 = tk.Button(root, text = "顯示", command=show, foreground = "#000")
# btn1.pack()

# btn2 = tk.Button(root, text = "清除", command=clear, foreground = "#000")
# btn2.pack()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")


# listbox = tk.Listbox(root, selectmode = "exxtended", selectforeground =  "white", selectbackground = "white") 
# listbox.insert(tk.END, 'Apple')    
# listbox.insert(tk.END, 'Banana')   
# listbox.insert(tk.END, 'orange')  
# listbox.pack()
# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")


# listbox = tk.Listbox(root)
# menu = ["TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA"]

# for i in menu:
#     listbox.insert(tk.END, i)
# listbox.pack()
# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# menu = tk.StringVar()
# menu.set(("TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA")) 
# listbox = tk.Listbox(root, listvariable = menu)
# listbox.pack()

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# def show():
#     n, = listbox.curselection()
#     text.set(listbox.get(n))

# text = tk.StringVar()
# label = tk.Label(root, textvariable = text)
# label.pack()

# menu = tk.StringVar()
# menu.set(("TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA")) 
# listbox = tk.Listbox(root, listvariable = menu, height = 8, selectmode = "extended")
# listbox.pack()

# btn = tk.Button(root, text = "顯示", command = show)
# btn.pack()

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# frame = tk.Frame(root, width = 15)
# frame.pack()

# scrolbar =  tk.Scrollbar(frame)
# scrolbar.pack(side="right", fill="y")

# menu = tk.StringVar()
# menu.set(("TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA","TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA","TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA")) 

# listbox = tk.Listbox(frame, listvariable = menu, height = 6, width = 15, yscrollcommand = scrolbar.set)
# listbox.pack(side = "right", fill = "y")

# scrolbar.config(command = listbox.yview)

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# def show (*e):
#     a.set(value.get())

# a = tk.StringVar()
# a.set("")

# label = tk.Label(root, textvariable=a)
# label.pack()

# optionlist = ["TAIWAN","JAPAN","AUSTRALIA","KOREA","CZECHIA"]
# value = tk.StringVar()
# value.set("")

# menu = tk.OptionMenu(root, value, *optionlist)
# menu.config(width=10, fg="white")
# menu.pack()

# value.trace_add("write", show)
# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# scale_v = tk.Scale(root, from_=0, to=1000)
# scale_v.pack()

# scale_h = tk.Scale(root, from_=0, to=10, orient="horizontal", 
#                    length=200, width=20, resolution=1, showvalue=True)
# scale_h.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# a = tk.StringVar()
# a.set("0,0")

# def show(e):

#    a.set(f'{scale_h.get()},{scale_v.get()}')

# label = tk.Label(root, textvariable=a)
# label.pack()

# scale_h = tk.Scale(root, from_=0, to=100, orient='horizontal', command=show)  
# scale_h.pack()

# scale_v = tk.Scale(root, from_=0, to=99, orient='vertical', command=show)  
# scale_v.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# spinbox = tk.Spinbox(root, from_=0, to=30, increment=1, 
#                      wrap=True, state="readonly")
# spinbox.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# a = tk.StringVar()
# a.set("0")

# def spin():
#     val = spinbox.get() 
#     a.set(val)           

# label = tk.Label(root, textvariable=a)  
# label.pack()

# spinbox = tk.Spinbox(root, from_=0, to=100, command=spin, 
#                      wrap=True, increment=5) 
# spinbox.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")
# frame1 = tk.Frame(root, pady=10, padx=10, bg='blue')              
# frame2 = tk.Frame(root, pady=10, padx=10, bg='black')              

# label = tk.Label(frame1, text='hello', width=10)   
# label.pack()

# labe2 = tk.Label(frame2, text='哈囉', width=10)   
# labe2.pack()

# frame2.pack()
# frame1.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# group = tk.LabelFrame(root, text='test', padx=10, pady=10)
# group.pack()

# a = tk.Label(group, text='AAA').pack()
# b = tk.Label(group, text='BBB').pack()
# c = tk.Label(group, text='CCC').pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# g1 = tk.LabelFrame(root, text="G1", padx=10, pady=10, bg = "orange")
# g1.grid(column=0, row=1, ipadx=10, ipady=10)

# a = tk.Label(g1, text='AAA').pack()
# b = tk.Label(g1, text='BBB').pack()
# c = tk.Label(g1, text='CCC').pack()

# g2 = tk.LabelFrame(root, text="G2", padx=10, pady=10, bg = "green")
# g2.grid(column=0, row=0, ipadx=10, ipady=10)

# d = tk.Label(g2, text='ddd').pack()
# f = tk.Label(g2, text='fff').pack()
# g = tk.Label(g2, text='ggg').pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# scrollbar = tk.Scrollbar(root)         
# scrollbar.pack(side='right', fill='y')  

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# frame = tk.Frame(root, height=10, width=15)  

# scrollbar = tk.Scrollbar(frame)               
# scrollbar.pack(side='right', fill='y')       


# text = tk.Text(frame, height=10, width=15, yscrollcommand=scrollbar.set)
# text.pack()

# scrollbar.config(command=text.yview)    
# frame.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# frame = tk.Frame(root, width=15)        
# frame.pack()

# scrollbar = tk.Scrollbar(frame)         
# scrollbar.pack(side='right', fill='y') 

# menu = tk.StringVar()
# menu.set(('1', '2', '3', '4', '5', '6', '7', '8', '9'))

# listbox = tk.Listbox(frame,  listvariable=menu, height=6, width=15, yscrollcommand=scrollbar.set)
# listbox.pack(side='left', fill='y')   

# scrollbar.config(command = listbox.yview) 

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# frame = tk.Frame(root, width=300, height=300)        
# frame.pack()

# canvas = tk.Canvas(frame, width=200, height=150, bg='blue', scrollregion=(0,0,400,400))

# canvas.create_rectangle(120, 10, 170, 100, width=8, fill='orange') 

# scroll_X = tk.Scrollbar(frame, orient='horizontal')  
# scroll_X.pack(side='bottom', fill='x')              
# scroll_X.config(command=canvas.xview)                

# scroll_y = tk.Scrollbar(frame, orient="vertical")
# scroll_y.pack(side='right', fill="y")
# scroll_y.config(command=canvas.xview)                

# canvas.config(xscrollcommand=scroll_X.set, yscrollcommand=scroll_y.set)  
# canvas.pack(side='left')                         

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# canvas = tk.Canvas(root, width=300, height=300)
# canvas.create_text(10, 0, text='hello', anchor='nw')
# canvas.create_text(30, 20, text='123', anchor='nw', font=('Arial', 90))
# canvas.create_text(30, 40, text='coco', anchor='nw', fill='#f00', 
#                    font=('Arial', 30, 'bold'))
# canvas.create_text(30, 100, text='嘿嘿', anchor='nw', fill='#0a0', 
#                    font=('Arial', 30, 'bold','italic','underline'))
# canvas.pack()

# root.mainloop()
# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# canvas = tk.Canvas(root, width=300, height=300)
# canvas.create_line(10,10,200,10)
# canvas.create_line(10, 30, 200, 30, width=10, fill="blue")
# canvas.create_line(10, 50, 200, 150, width=10, fill='red')
# canvas.create_line(10, 70, 200, 200, width=10, fill='black', dash=(1,10))
# canvas.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# canvas = tk.Canvas(root, width=300, height=300)
# canvas.create_rectangle(10, 10, 50, 100)
# canvas.create_rectangle(60, 10, 110, 100, width=8)
# canvas.create_rectangle(120, 10, 170, 100, width=8, fill='blue')
# canvas.create_rectangle(180, 10, 230, 100, width=8, fill='orange')
# canvas.create_rectangle(240, 10, 290, 100, width=3, fill='green', outline='yellow', dash=(5, 10))
# canvas.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# canvas = tk.Canvas(root, width=300, height=300)

# canvas.create_oval(10, 10, 50, 100)
# canvas.create_oval(60, 10, 100, 100, width=8)
# canvas.create_oval(110, 10, 150, 100, width=8, fill='black')
# canvas.create_oval(160, 10, 200, 100, width=8, fill='blue', outline='white')
# canvas.create_oval(210, 10, 250, 100, width=3, fill='white', outline='green', dash=(5,5))

# canvas.create_oval(10, 120, 50, 160)
# canvas.create_oval(60, 120, 100, 160, width=8)
# canvas.create_oval(110, 120, 150, 160, width=8, fill='yellow')
# canvas.create_oval(160, 120, 200, 160, width=8, fill='red', outline='white')
# canvas.create_oval(210, 120, 250, 160, width=3, fill='orange', outline='black', dash=(5,5))

# canvas.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")
# canvas = tk.Canvas(root, width=500, height=500)

# canvas.create_arc(10, 10, 100, 100, extent=180)
# canvas.create_arc(110, 10, 200, 100, start=60, extent=180)
# canvas.create_arc(210, 10, 300, 100, start=60, extent=70, width=8, fill='#f00')
# canvas.create_arc(10, 110, 100, 200, start=60, extent=180, width=8, fill='#f00', outline='#00f')
# canvas.create_arc(110, 110, 200, 200, start=-60, extent=-120, width=3, fill='#fff', outline='#0a0', 
#                   dash=(5,5))
# canvas.create_arc(210, 110, 300, 200, start=-60, extent=-90, width=3, fill='#ff0', outline='#f50', 
#                   dash=(5,10), style='arc')
# canvas.create_arc(10, 210, 100, 300, start=-60, extent=-90, width=3, fill='#f000f0', outline='#f05000', 
#                   dash=(5,5), style='chord')
# canvas.create_arc(110, 210, 100, 300, start=-60, extent=-180, width=0, fill='#f000f0', outline='#f05000', 
#                   dash=(5,5), style='chord')
# canvas.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# canvas = tk.Canvas(root, width=300, height=300)

# canvas.create_oval(10, 50, 280,300)
# canvas.create_oval(100, 120, 80, 180, width=0, fill='black')
# canvas.create_oval(200, 120, 180, 180, width=0, fill='black')
# canvas.create_arc(50, 100, 240, 280, start=-180, extent=180, width=0, fill='black', outline='black')


# canvas.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# canvas = tk.Canvas(root, width=300, height=300)

# canvas.create_polygon([10,10, 10,20, 50,10, 50,20, 50,10,50,20, ], outline="blue")
# canvas.create_polygon([110,10, 110,20, 150,10, 150,20, 150,10, 150,20, ], outline="green", fill='')
# canvas.create_polygon([10,110, 10,120, 50,110, 50,120, 50,110, 50,120, ], outline="black", fill='#fff', width=5)
# canvas.create_polygon([110,110, 110,120, 150,110, 150,120, 150,110, 150,120, ], outline="red", fill='', width=3, dash=(5,5))

# canvas.pack()

# root.mainloop()

# import cv2
# import numpy as np

# img = cv2.imread('埼玉西武獅.jpg')

# def floodFill(source, mask, seedPoint, newVal, loDiff, upDiff, flags=cv2.FLOODFILL_FIXED_RANGE):
#     result = source.copy()
#     cv2.floodFill(result, mask=mask, seedPoint=seedPoint, newVal=newVal, loDiff=loDiff, upDiff=upDiff, flags=flags)
#     return result

# h, w = img.shape[:2]                     
# mask = np.zeros((h+2,w+2,1), np.uint8)   
# output = floodFill(img, mask, (100,10), (0,0,255), (100,100,60), (100,100,100))

# cv2.imshow('oxxostudio', output)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# import tkinter as  tk
# from PIL import Image, ImageTk

# root = tk.Tk()
# root.title("test")
# root.geometry("600x400+50+50")
# img = Image.open('埼玉西武獅.jpg')
# tk_img = ImageTk.PhotoImage(img)

# canvas = tk.Canvas(root, width=300, height=300)
# canvas.create_image(0, 0, anchor='nw', image=tk_img)

# canvas.pack()

# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# menubar = tk.Menu(root)

# filemenu = tk.Menu(menubar)
# filemenu.add_command(label="open")
# filemenu.add_command(label="save")
# filemenu.add_separator()
# filemenu.add_command(label="close")

# menubar.add_cascade(label="save", menu=filemenu)
# root.config(menu=menubar)
# root.mainloop()

# import tkinter as  tk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# menu = tk.Menu(root)

# menubar_1 = tk.Menu(menu)
# menubar_1.add_command(label="開啟")
# menubar_1.add_command(label="存檔")
# menubar_1.add_separator()
# menubar_1.add_command(label="關閉")
# menu.add_cascade(label="檔案", menu=menubar_1)

# menubar_2 = tk.Menu(menu)
# menubar_2.add_command(label="復原")
# menubar_2.add_command(label="重做")
# menubar_2.add_separator()

# menubar_2_more = tk.Menu(menubar_2)
# menubar_2_more.add_command(label="複製")
# menubar_2_more.add_command(label="貼上")
# menubar_2_more.add_command(label="剪下")
# menubar_2.add_cascade(label="其他", menu=menubar_2_more)

# menu.add_cascade(label="編輯", menu=menubar_2)

# root.config(menu=menu)

# root.mainloop()

# import tkinter as  tk
# from PIL import Image, ImageTk

# root = tk.Tk()
# root.title("test")
# root.geometry("640x480+50+100")

# img = Image.open("埼玉西武獅.jpg")
# img = img.resize((20,30))
# icon = ImageTk.PhotoImage(img)

# img1 = Image.open("Mona.png")
# img1 = img1.resize((20,30))
# icon1 = ImageTk.PhotoImage(img1)

# img2 = Image.open("logo1.ico")
# img2 = img2.resize((20,30))
# icon2 = ImageTk.PhotoImage(img2)

# menu = tk.Menu(root)

# menubar = tk.Menu(menu)
# menubar.add_command(label="開啟", image=icon, compound="left")
# menubar.add_command(label="存檔", image=icon1, compound="left")
# menubar.add_command(label="關閉", image=icon2, compound="left")
# menu.add_cascade(label="檔案", menu=menubar)

# root.config(menu=menu)

# root.mainloop()

# import tkinter as  tk
# from PIL import Image, ImageTk


# root = tk.Tk()
# root.title('text')
# root.geometry('200x200')

# def open(event=None):
#     print('開啟')
#     menubar.entryconfig("儲存", state="normal")
    
# def save(event=None):
#     # Check if the save menu item is enabled before proceeding
#     if menubar.entrycget("儲存", "state") == "normal":
#         print('儲存')

# def close(event=None):
#     print('關閉')
#     menubar.entryconfig("儲存", state="disabled")
        
# def exit(event=None):
#     print('離開')
#     root.destroy()

# def show_file_menu(event=None):
#     x= root.winfo_rootx()
#     y =root.winfo_rooty() +10

#     menubar.post(x, y)
#     return"break"

# img = Image.open("埼玉西武獅.jpg")
# img = img.resize((20,30))
# icon = ImageTk.PhotoImage(img)

# menu = tk.Menu(root)

# menubar = tk.Menu(menu)
# menubar.add_command(label="開啟", image=icon, compound='right', command=open, accelerator='Ctrl+O', state="normal")
# menubar.add_command(label="儲存", command=save, accelerator='Ctrl+S', state="disabled")
# menubar.add_command(label="關閉", command=close, accelerator='Ctrl+l', state="normal")
# menubar.add_command(label="離開", command=exit, accelerator='Ctrl+Q', state="normal")
# menu.add_cascade(label="檔案", menu=menubar)

# root.bind_all("<<Control->-o>", open)
# root.bind_all("<<Control->s>", save)
# root.bind_all("<Control-s>", exit)

# root.bind_all("<Control-f>", show_file_menu)

# root.config(menu=menu)

# root.mainloop()




# import tkinter as tk
# from PIL import Image, ImageTk

# root = tk.Tk()
# root.title('oxxo.studio')
# root.geometry('200x200')

# def open(event=None):
#     print('開啟')
#     menubar.entryconfig("儲存", state="normal")
    
# def save(event=None):
#     # Check if the save menu item is enabled before proceeding
#     if menubar.entrycget("儲存", "state") == "normal":
#         print('儲存')

# def close(event=None):
#     print('關閉')
#     menubar.entryconfig("儲存", state="disabled")
        
# def exit(event=None):
#     print('離開')
#     root.destroy()
    
# def show_file_menu(event=None):
#     x = root.winfo_rootx()
#     y = root.winfo_rooty() + 10
#     menubar.post(x, y)

# def handle_file_menu(event=None):

#     temp_menu = tk.Menu(root, tearoff=0)
#     temp_menu.add_command(label="開啟(o)", command=open)
#     temp_menu.add_command(label="儲存(s)", command=save)
#     temp_menu.add_command(label="關閉(q)", command=close)

#     temp_menu.post(root.winfo_pointerx(), root.winfo_pointery())

#     return "break"

# img = Image.open("埼玉西武獅.jpg")
# img = img.resize((20, 20))
# icon = ImageTk.PhotoImage(img)

# menu = tk.Menu(root)

# menubar = tk.Menu(menu)
# menubar.add_command(label="開啟", image=icon, compound='right', command=open, accelerator='Ctrl+O', state="normal")
# menubar.add_command(label="儲存", command=save, accelerator='Ctrl+S', state="disabled")
# menubar.add_command(label="關閉", command=close, accelerator='Ctrl+l', state="normal")
# menubar.add_command(label="離開", command=exit, accelerator='Ctrl+Q', state="normal")
# menu.add_cascade(label="檔案", menu=menubar)

# root.bind_all("<Control-o>", open)
# root.bind_all("<Control-s>", save)
# root.bind_all("<Control-l>", close)
# root.bind_all("<Control-q>", exit)
# root.bind_all("<Control-f>", show_file_menu)


# root.config(menu=menu)

# root.mainloop()

# import tkinter as tk
# from tkinter import messagebox

# messagebox.showinfo("顯示訊息", "訊息測試")

# import tkinter as tk
# from tkinter import messagebox

# root = tk.Tk()
# root.title('test')
# root.geometry('200x300')

# def showinfo():
#      messagebox.showinfo('訊息視窗', '顯示訊息')
# btn_showinfo = tk.Button(root, text='訊息按鈕', command=showinfo)
# btn_showinfo.pack()

# def showwarning():
#      messagebox.showwarning('訊息視窗', '警告訊息')
# btn_showwarning = tk.Button(root, text='訊息按鈕', command=showwarning)
# btn_showwarning.pack()

# def showerror():
#      messagebox.showerror('錯誤視窗', '錯誤訊息')
# btn_showerror = tk.Button(root, text='錯誤按鈕', command=showerror)
# btn_showerror.pack()

# def askquestion():
#      messagebox.askquestion('問題視窗', '問題訊息')
# btn_askquestion = tk.Button(root, text='問題按鈕', command=askquestion)
# btn_askquestion.pack()

# def askyesno():
#      messagebox.askyesno('是否視窗', '是否訊息')
# btn_askyesno = tk.Button(root, text='是否按鈕', command=askyesno)
# btn_askyesno.pack()

# def askokcancel():
#      messagebox.askokcancel('確認視窗', '確認取消訊息')
# btn_askokcancel = tk.Button(root, text='確認取消按鈕', command=askokcancel)
# btn_askokcancel.pack()

# def askretrycancel():
#      messagebox.askretrycancel('重試取消視窗', '重試取消訊息')
# btn_askretrycancel = tk.Button(root, text='重試取消按鈕', command=askretrycancel)
# btn_askretrycancel.pack()

# root.mainloop()
# import tkinter as tk
# from tkinter import messagebox

# root = tk.Tk()
# root.title("text")
# root.geometry('200x300')

# def askyesno():
#   result = messagebox.askyesno('是否關閉視窗', '是否要離開視窗')
#   print(result)

# btn_askyesno = tk.Button(root, text='關閉視窗', command=askyesno)
# btn_askyesno.pack()

# root.mainloop()

# import tkinter as tk
# from PIL import Image, ImageTk

# root = tk.Tk()
# root.title("text")
# root.geometry('900x600')

# img = Image.open("Mona.webp")
# tk_img = ImageTk.PhotoImage(img)

# Canva = tk.Canvas(root, width=1000, height=1000)
# Canva.create_image(30, 20, anchor="nw", image=tk_img)
# Canva.pack()

# root.mainloop()

# import  tkinter as tk 
# from tkinter import ttk 

# root = tk.Tk()
# root.title("text")
# root.geometry("200x600")

# bar = ttk.Progressbar(root)
# # bar = ttk.Progressbar(root, orient="horizontal", length=200, mode="determinate")
# # bar = ttk.Progressbar(root, orient="vertical", length=200, mode="indeterminate")
# bar.pack(pady=20)
# bar.step(60)
# bar.start(10)

# root.mainloop()
# import tkinter as tk

# root = tk.Tk()
# root.title("text")
# root.geometry('200x200')

# a = tk.Label(root, text='AAA', background='#ff9910')
# b = tk.Label(root, text='BBB', background="#4800FF")

# a.pack(fill='both')
# b.pack(fill='both', side='left')

# root.mainloop()

# import tkinter as tk

# root = tk.Tk()
# root.title("text")
# root.geometry('200x200')

# a = tk.Label(root, text='AAA', background='#ff9910')
# b = tk.Label(root, text='BBB', background="#4800FF")

# a.pack(fill='x', expand=1)
# b.pack(fill='both', expand=1)

# root.mainloop()

# import tkinter as tk

# root = tk.Tk()
# root.title("text")
# root.geometry('200x200')

# a = tk.Label(root, text='AAA', background='#ff9910')
# b = tk.Label(root, text='BBB', background="#4800FF")
# c = tk.Label(root, text='BBB', background="#300992")
# d = tk.Label(root, text='BBB', background="#09FF00")
# e = tk.Label(root, text='BBB', background="#FF0000")
# f = tk.Label(root, text='BBB', background="#FF00B7")
# g = tk.Label(root, text='BBB', background="#00A6FF")

# a.pack(fill='both', expand=1)
# b.pack(fill='both', expand=1)
# c.pack(fill='both', expand=1)
# d.pack(fill='both', expand=1)
# e.pack(fill='both', expand=1)
# f.pack(fill='both', expand=1)
# g.pack(fill='both', expand=1)
# root.mainloop()
# import tkinter as tk

# root = tk.Tk()
# root.title("text")
# root.geometry('200x200')

# colors = [
#     '#ff9910',  # 橘色
#     '#4800FF',  # 藍色
#     '#300992',  # 紫色
#     '#09FF00',  # 綠色
#     '#FF0000',  # 紅色
#     '#FF00B7',  # 粉紅色
#     '#00A6FF'   # 亮藍色
# ]

# for color in colors:
#     label = tk.Label(root, text='Stripe', background=color)
#     label.pack(fill='both', expand=1)

# root.mainloop()

# import tkinter as tk

# root = tk.Tk()
# root.title('text')
# root.geometry('700x700')

# a = tk.Label(root, text='AAA', background='#f90')
# b = tk.Label(root, text='BBB', background='#09c')
# c = tk.Label(root, text='CCC', background='#fc0')
# d = tk.Label(root, text='DDD', background='#0c9')

# a.place(relx=0.1, rely=0.1)
# b.place(relx=0.3, rely=0.3)
# c.place(relx=0.5, rely=0.5)
# d.place(relx=0.7, rely=0.7)

# root.mainloop()

import tkinter as tk
import datetime

root = tk.Tk()
root.title('text')
root.geometry('680x400+50+100')

GMT = datetime.timezone(datetime.timedelta(hours=8))    

a = tk.StringVar()         

def showTime():
    now = datetime.datetime.now(tz=GMT).strftime('%H:%M:%S')  
    a.set(now)                    
    root.after(1000, showTime)   

tk.Label(root, text='目前時間', font=('Arial',20)).pack()   
tk.Label(root, textvariable=a, font=('Arial',20)).pack()  

showTime()   

root.mainloop()