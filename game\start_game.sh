#!/bin/bash

# 末日生存射擊遊戲啟動腳本

echo ""
echo "========================================"
echo "🎮 末日生存射擊遊戲服務器"
echo "========================================"
echo ""

# 檢查Python是否安裝
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 未找到Python"
        echo "💡 請先安裝Python: https://python.org"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ 找到Python: $($PYTHON_CMD --version)"

# 檢查必要文件
if [ ! -f "index.html" ]; then
    echo "❌ 未找到 index.html"
    echo "💡 請確保在遊戲目錄中運行此腳本"
    exit 1
fi

if [ ! -f "js/game.js" ]; then
    echo "❌ 未找到 js/game.js"
    echo "💡 請確保遊戲文件完整"
    exit 1
fi

echo "✅ 文件檢查完成"
echo "🚀 啟動遊戲服務器..."
echo ""

# 設置腳本為可執行
chmod +x "$0"

# 啟動Python服務器
$PYTHON_CMD start_server.py

echo ""
echo "🛑 服務器已停止"
