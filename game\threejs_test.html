<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Three.js 測試頁面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #4ecdc4;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }

        .success {
            background: rgba(0, 255, 0, 0.2);
            border-left: 3px solid #00ff00;
        }

        .error {
            background: rgba(255, 0, 0, 0.2);
            border-left: 3px solid #ff0000;
        }

        .warning {
            background: rgba(255, 255, 0, 0.2);
            border-left: 3px solid #ffff00;
        }

        #testCanvas {
            width: 100%;
            height: 300px;
            border: 2px solid #4ecdc4;
            border-radius: 10px;
            background: #000;
        }

        .button {
            padding: 10px 20px;
            background: #4ecdc4;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .button:hover {
            background: #45b7aa;
        }

        .game-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }

        .game-link {
            display: block;
            padding: 15px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
        }

        .game-link:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Three.js 測試診斷</h1>
        
        <div class="test-section">
            <h3>📦 Three.js 載入測試</h3>
            <div id="loadTest"></div>
        </div>
        
        <div class="test-section">
            <h3>🧪 組件測試</h3>
            <div id="componentTest"></div>
        </div>
        
        <div class="test-section">
            <h3>🎮 渲染器測試</h3>
            <canvas id="testCanvas"></canvas>
            <div id="rendererTest"></div>
            <button class="button" onclick="testRenderer()">測試渲染器</button>
        </div>
        
        <div class="test-section">
            <h3>🎯 遊戲兼容性測試</h3>
            <div id="gameTest"></div>
            <button class="button" onclick="testGameCompatibility()">測試遊戲兼容性</button>
        </div>
        
        <div class="game-links">
            <a href="index.html" class="game-link">🎮 Three.js版遊戲</a>
            <a href="game_offline.html" class="game-link">📱 離線版遊戲</a>
            <a href="game_launcher.html" class="game-link">🚀 遊戲啟動器</a>
            <a href="game_status.html" class="game-link">📊 狀態檢查</a>
        </div>
    </div>

    <!-- Three.js 庫 -->
    <script src="js/three.min.js"></script>
    
    <script>
        function addResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function testThreeJSLoad() {
            const container = 'loadTest';
            
            if (typeof THREE === 'undefined') {
                addResult(container, '❌ THREE 對象未定義', 'error');
                return false;
            }
            
            addResult(container, '✅ THREE 對象已載入', 'success');
            addResult(container, `📦 版本: r${THREE.REVISION}`, 'success');
            addResult(container, `🔧 組件數量: ${Object.keys(THREE).length}`, 'success');
            
            return true;
        }
        
        function testComponents() {
            const container = 'componentTest';
            
            const requiredComponents = [
                'Vector3', 'Scene', 'PerspectiveCamera', 'WebGLRenderer',
                'BoxGeometry', 'SphereGeometry', 'PlaneGeometry',
                'MeshBasicMaterial', 'MeshLambertMaterial', 'MeshPhongMaterial',
                'Mesh', 'Group', 'AmbientLight', 'DirectionalLight',
                'Color', 'Clock', 'Fog'
            ];
            
            let missingComponents = [];
            let foundComponents = [];
            
            requiredComponents.forEach(component => {
                if (typeof THREE[component] === 'function') {
                    foundComponents.push(component);
                } else {
                    missingComponents.push(component);
                }
            });
            
            foundComponents.forEach(component => {
                addResult(container, `✅ ${component} 已載入`, 'success');
            });
            
            missingComponents.forEach(component => {
                addResult(container, `❌ ${component} 缺失`, 'error');
            });
            
            if (missingComponents.length === 0) {
                addResult(container, '🎉 所有必需組件都已載入', 'success');
            } else {
                addResult(container, `⚠️ 缺少 ${missingComponents.length} 個組件`, 'warning');
            }
        }
        
        function testRenderer() {
            const container = 'rendererTest';
            const canvas = document.getElementById('testCanvas');
            
            try {
                // 清除之前的結果
                document.getElementById(container).innerHTML = '';
                
                // 創建渲染器
                const renderer = new THREE.WebGLRenderer({ canvas: canvas });
                addResult(container, '✅ WebGLRenderer 創建成功', 'success');
                
                // 設置大小
                renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                addResult(container, '✅ 渲染器大小設置成功', 'success');
                
                // 創建場景
                const scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                addResult(container, '✅ 場景創建成功', 'success');
                
                // 創建相機
                const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
                camera.position.z = 5;
                addResult(container, '✅ 相機創建成功', 'success');
                
                // 創建幾何體和材質
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                addResult(container, '✅ 立方體創建成功', 'success');
                
                // 渲染
                renderer.render(scene, camera);
                addResult(container, '✅ 渲染成功', 'success');
                
                addResult(container, '🎉 渲染器測試完全通過', 'success');
                
            } catch (error) {
                addResult(container, `❌ 渲染器測試失敗: ${error.message}`, 'error');
            }
        }
        
        function testGameCompatibility() {
            const container = 'gameTest';
            
            try {
                // 清除之前的結果
                document.getElementById(container).innerHTML = '';
                
                // 測試遊戲需要的特定功能
                
                // 測試Vector3操作
                const vec = new THREE.Vector3(1, 2, 3);
                vec.normalize();
                addResult(container, '✅ Vector3 操作正常', 'success');
                
                // 測試Object3D
                const obj = new THREE.Object3D();
                obj.position.set(1, 2, 3);
                addResult(container, '✅ Object3D 操作正常', 'success');
                
                // 測試Mesh
                const geometry = new THREE.SphereGeometry(1, 8, 6);
                const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
                const mesh = new THREE.Mesh(geometry, material);
                addResult(container, '✅ Mesh 創建正常', 'success');
                
                // 測試Group
                const group = new THREE.Group();
                group.add(mesh);
                addResult(container, '✅ Group 操作正常', 'success');
                
                // 測試光照
                const light = new THREE.DirectionalLight(0xffffff, 1);
                light.position.set(1, 1, 1);
                addResult(container, '✅ 光照創建正常', 'success');
                
                // 測試時鐘
                const clock = new THREE.Clock();
                const delta = clock.getDelta();
                addResult(container, '✅ 時鐘功能正常', 'success');
                
                addResult(container, '🎉 遊戲兼容性測試完全通過', 'success');
                addResult(container, '🎮 遊戲應該可以正常運行', 'success');
                
            } catch (error) {
                addResult(container, `❌ 兼容性測試失敗: ${error.message}`, 'error');
            }
        }
        
        // 頁面載入時自動運行測試
        window.addEventListener('load', function() {
            console.log('🔧 開始Three.js診斷測試...');
            
            setTimeout(() => {
                if (testThreeJSLoad()) {
                    testComponents();
                }
            }, 100);
        });
    </script>
</body>
</html>
