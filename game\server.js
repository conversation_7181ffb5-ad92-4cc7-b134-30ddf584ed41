const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const PORT = process.env.PORT || 8000;

// MIME類型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

const server = http.createServer((req, res) => {
    console.log(`🌐 ${req.method} ${req.url}`);
    
    // 設置CORS頭部
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }
    
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';
    
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // 文件不存在
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <html>
                        <head><title>404 - 文件未找到</title></head>
                        <body style="font-family: Arial; text-align: center; padding: 50px;">
                            <h1>🚫 404 - 文件未找到</h1>
                            <p>請求的文件 <code>${req.url}</code> 不存在</p>
                            <a href="/">返回首頁</a>
                        </body>
                    </html>
                `);
            } else {
                // 服務器錯誤
                res.writeHead(500);
                res.end(`服務器錯誤: ${error.code}`);
            }
        } else {
            // 成功返回文件
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(PORT, () => {
    console.log('🎮 末日生存射擊遊戲服務器');
    console.log('=' * 40);
    console.log(`🌐 服務器運行在: http://localhost:${PORT}`);
    console.log(`📁 服務目錄: ${__dirname}`);
    console.log('📝 按 Ctrl+C 停止服務器');
    console.log('=' * 40);
    
    // 自動打開瀏覽器
    const url = `http://localhost:${PORT}`;
    const start = process.platform === 'darwin' ? 'open' : 
                  process.platform === 'win32' ? 'start' : 'xdg-open';
    
    exec(`${start} ${url}`, (error) => {
        if (error) {
            console.log('⚠️ 無法自動打開瀏覽器，請手動訪問:', url);
        } else {
            console.log('🌐 已自動打開瀏覽器');
        }
    });
});

server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.log(`❌ 端口 ${PORT} 已被占用`);
        console.log('💡 請嘗試使用其他端口: PORT=8001 node server.js');
    } else {
        console.log(`❌ 服務器錯誤: ${error.message}`);
    }
});

process.on('SIGINT', () => {
    console.log('\n🛑 服務器已停止');
    process.exit(0);
});
