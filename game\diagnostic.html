<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 遊戲診斷工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #4ecdc4;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .section h3 {
            margin-bottom: 15px;
            color: #ff6b6b;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        .status-good {
            color: #00ff00;
        }

        .status-warning {
            color: #ffaa00;
        }

        .status-error {
            color: #ff0000;
        }

        .test-button {
            padding: 10px 20px;
            background: #4ecdc4;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .test-button:hover {
            background: #45b7aa;
        }

        .log {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .game-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .game-link {
            display: block;
            padding: 15px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
        }

        .game-link:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 遊戲診斷工具</h1>
        
        <!-- 系統檢查 -->
        <div class="section">
            <h3>🖥️ 系統檢查</h3>
            <div class="status-item">
                <span>瀏覽器:</span>
                <span id="browserInfo" class="status-good">檢查中...</span>
            </div>
            <div class="status-item">
                <span>WebGL支援:</span>
                <span id="webglInfo" class="status-good">檢查中...</span>
            </div>
            <div class="status-item">
                <span>服務器連接:</span>
                <span id="serverInfo" class="status-good">檢查中...</span>
            </div>
        </div>
        
        <!-- 文件檢查 -->
        <div class="section">
            <h3>📁 文件檢查</h3>
            <div class="status-item">
                <span>Three.js庫:</span>
                <span id="threejsInfo" class="status-good">檢查中...</span>
            </div>
            <div class="status-item">
                <span>遊戲主程式:</span>
                <span id="gameJsInfo" class="status-good">檢查中...</span>
            </div>
            <div class="status-item">
                <span>HTML文件:</span>
                <span id="htmlInfo" class="status-good">檢查中...</span>
            </div>
        </div>
        
        <!-- 功能測試 -->
        <div class="section">
            <h3>🧪 功能測試</h3>
            <button class="test-button" onclick="testThreeJS()">測試Three.js</button>
            <button class="test-button" onclick="testMouseEvents()">測試鼠標事件</button>
            <button class="test-button" onclick="testScope()">測試瞄準鏡</button>
            <button class="test-button" onclick="clearLog()">清除日誌</button>
            
            <div id="testLog" class="log"></div>
        </div>
        
        <!-- 遊戲連結 -->
        <div class="section">
            <h3>🎮 遊戲版本</h3>
            <div class="game-links">
                <a href="game_offline.html" class="game-link">📱 離線版 (推薦)</a>
                <a href="index.html" class="game-link">🌐 原版3D</a>
                <a href="game_3d_basic.html" class="game-link">🔧 基本3D版</a>
                <a href="scope_test.html" class="game-link">🔍 瞄準鏡測試</a>
                <a href="test_mouse_events.html" class="game-link">🖱️ 鼠標測試</a>
                <a href="game_launcher.html" class="game-link">🚀 遊戲啟動器</a>
            </div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('testLog');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff0000' : type === 'success' ? '#00ff00' : '#ffffff';
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.innerHTML = '';
            log('日誌已清除');
        }
        
        function updateStatus(elementId, status, type = 'good') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = status;
                element.className = `status-${type}`;
            }
        }
        
        // 檢查瀏覽器
        function checkBrowser() {
            const userAgent = navigator.userAgent;
            let browser = '未知';
            
            if (userAgent.includes('Chrome')) browser = 'Chrome';
            else if (userAgent.includes('Firefox')) browser = 'Firefox';
            else if (userAgent.includes('Safari')) browser = 'Safari';
            else if (userAgent.includes('Edge')) browser = 'Edge';
            
            updateStatus('browserInfo', browser, 'good');
            log(`瀏覽器: ${browser}`);
        }
        
        // 檢查WebGL
        function checkWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    updateStatus('webglInfo', '支援', 'good');
                    log('WebGL支援正常', 'success');
                } else {
                    updateStatus('webglInfo', '不支援', 'error');
                    log('WebGL不支援', 'error');
                }
            } catch (e) {
                updateStatus('webglInfo', '檢查失敗', 'error');
                log('WebGL檢查失敗: ' + e.message, 'error');
            }
        }
        
        // 檢查服務器
        function checkServer() {
            fetch(window.location.origin)
                .then(response => {
                    if (response.ok) {
                        updateStatus('serverInfo', '正常', 'good');
                        log('服務器連接正常', 'success');
                    } else {
                        updateStatus('serverInfo', '異常', 'warning');
                        log('服務器響應異常', 'error');
                    }
                })
                .catch(error => {
                    updateStatus('serverInfo', '失敗', 'error');
                    log('服務器連接失敗: ' + error.message, 'error');
                });
        }
        
        // 檢查Three.js
        function checkThreeJS() {
            fetch('js/three.min.js')
                .then(response => {
                    if (response.ok) {
                        updateStatus('threejsInfo', `存在 (${Math.round(response.headers.get('content-length')/1024)}KB)`, 'good');
                        log('Three.js文件存在', 'success');
                    } else {
                        updateStatus('threejsInfo', '不存在', 'error');
                        log('Three.js文件不存在', 'error');
                    }
                })
                .catch(error => {
                    updateStatus('threejsInfo', '檢查失敗', 'error');
                    log('Three.js檢查失敗: ' + error.message, 'error');
                });
        }
        
        // 檢查遊戲JS
        function checkGameJS() {
            fetch('js/game.js')
                .then(response => {
                    if (response.ok) {
                        updateStatus('gameJsInfo', `存在 (${Math.round(response.headers.get('content-length')/1024)}KB)`, 'good');
                        log('遊戲主程式存在', 'success');
                    } else {
                        updateStatus('gameJsInfo', '不存在', 'error');
                        log('遊戲主程式不存在', 'error');
                    }
                })
                .catch(error => {
                    updateStatus('gameJsInfo', '檢查失敗', 'error');
                    log('遊戲主程式檢查失敗: ' + error.message, 'error');
                });
        }
        
        // 檢查HTML文件
        function checkHTML() {
            const files = ['index.html', 'game_offline.html', 'game_3d_basic.html'];
            let existingFiles = 0;
            
            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            existingFiles++;
                            log(`${file} 存在`);
                        }
                        
                        if (existingFiles === files.length) {
                            updateStatus('htmlInfo', '全部存在', 'good');
                        } else if (existingFiles > 0) {
                            updateStatus('htmlInfo', `部分存在 (${existingFiles}/${files.length})`, 'warning');
                        } else {
                            updateStatus('htmlInfo', '不存在', 'error');
                        }
                    })
                    .catch(error => {
                        log(`${file} 檢查失敗: ${error.message}`, 'error');
                    });
            });
        }
        
        // 測試Three.js
        function testThreeJS() {
            log('開始測試Three.js...');
            
            const script = document.createElement('script');
            script.src = 'js/three.min.js';
            script.onload = function() {
                if (typeof THREE !== 'undefined') {
                    log('Three.js載入成功', 'success');
                    log(`Three.js版本: ${THREE.REVISION}`);
                    
                    // 測試基本功能
                    try {
                        const scene = new THREE.Scene();
                        const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
                        log('Three.js基本功能測試通過', 'success');
                    } catch (e) {
                        log('Three.js功能測試失敗: ' + e.message, 'error');
                    }
                } else {
                    log('Three.js載入失敗', 'error');
                }
            };
            script.onerror = function() {
                log('Three.js載入錯誤', 'error');
            };
            document.head.appendChild(script);
        }
        
        // 測試鼠標事件
        function testMouseEvents() {
            log('開始測試鼠標事件...');
            log('請點擊左鍵和右鍵進行測試');
            
            let leftClicks = 0;
            let rightClicks = 0;
            
            function handleMouseDown(e) {
                if (e.button === 0) {
                    leftClicks++;
                    log(`左鍵點擊 #${leftClicks}`, 'success');
                } else if (e.button === 2) {
                    rightClicks++;
                    log(`右鍵點擊 #${rightClicks}`, 'success');
                }
            }
            
            document.addEventListener('mousedown', handleMouseDown);
            
            setTimeout(() => {
                document.removeEventListener('mousedown', handleMouseDown);
                log(`鼠標測試完成 - 左鍵: ${leftClicks}, 右鍵: ${rightClicks}`);
            }, 10000);
        }
        
        // 測試瞄準鏡
        function testScope() {
            log('開始測試瞄準鏡功能...');
            
            // 創建測試瞄準鏡
            const scope = document.createElement('div');
            scope.id = 'testScope';
            scope.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 200px;
                height: 200px;
                border: 2px solid #ff0000;
                border-radius: 50%;
                background: rgba(0, 0, 0, 0.3);
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 10000;
                pointer-events: none;
            `;
            
            // 添加十字線
            scope.innerHTML = `
                <div style="position: absolute; top: 50%; left: 0; width: 100%; height: 1px; background: #ff0000; transform: translateY(-50%);"></div>
                <div style="position: absolute; left: 50%; top: 0; width: 1px; height: 100%; background: #ff0000; transform: translateX(-50%);"></div>
            `;
            
            document.body.appendChild(scope);
            
            // 測試顯示/隱藏
            setTimeout(() => {
                scope.style.opacity = '1';
                log('瞄準鏡顯示測試', 'success');
            }, 1000);
            
            setTimeout(() => {
                scope.style.opacity = '0';
                log('瞄準鏡隱藏測試', 'success');
            }, 3000);
            
            setTimeout(() => {
                document.body.removeChild(scope);
                log('瞄準鏡功能測試完成', 'success');
            }, 5000);
        }
        
        // 初始化檢查
        window.addEventListener('load', function() {
            log('開始系統診斷...');
            
            checkBrowser();
            checkWebGL();
            checkServer();
            checkThreeJS();
            checkGameJS();
            checkHTML();
            
            log('診斷完成，請查看上方狀態');
        });
        
        // 禁用右鍵菜單
        document.addEventListener('contextmenu', e => e.preventDefault());
    </script>
</body>
</html>
