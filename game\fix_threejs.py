#!/usr/bin/env python3
"""
修復Three.js載入問題 - 創建本地版本
"""

import urllib.request
import os
import shutil

def download_threejs_alternative():
    """嘗試從多個CDN下載Three.js"""
    
    # 多個Three.js CDN源
    cdn_urls = [
        "https://unpkg.com/three@0.150.0/build/three.min.js",
        "https://cdn.jsdelivr.net/npm/three@0.150.0/build/three.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js",
        "https://threejs.org/build/three.min.js"
    ]
    
    # 確保js目錄存在
    os.makedirs("js", exist_ok=True)
    local_path = "js/three.min.js"
    
    print("🔄 嘗試從多個CDN下載Three.js...")
    
    for i, url in enumerate(cdn_urls, 1):
        print(f"\n📥 嘗試源 {i}: {url}")
        try:
            urllib.request.urlretrieve(url, local_path)
            file_size = os.path.getsize(local_path)
            
            if file_size > 100000:  # 檢查文件大小是否合理
                print(f"✅ 下載成功！文件大小: {file_size:,} 字節")
                return True
            else:
                print(f"⚠️ 文件太小，可能下載不完整")
                
        except Exception as e:
            print(f"❌ 下載失敗: {e}")
            continue
    
    return False

def create_embedded_threejs():
    """創建內嵌簡化Three.js的版本"""
    
    print("🔧 創建內嵌Three.js版本...")
    
    # 簡化的Three.js核心功能（僅包含基本功能）
    mini_threejs = '''
// 簡化版Three.js - 僅包含基本3D功能
window.THREE = {};

// 基本數學類
THREE.Vector3 = function(x = 0, y = 0, z = 0) {
    this.x = x; this.y = y; this.z = z;
    this.set = function(x, y, z) { this.x = x; this.y = y; this.z = z; return this; };
    this.copy = function(v) { this.x = v.x; this.y = v.y; this.z = v.z; return this; };
    this.clone = function() { return new THREE.Vector3(this.x, this.y, this.z); };
    this.add = function(v) { this.x += v.x; this.y += v.y; this.z += v.z; return this; };
    this.sub = function(v) { this.x -= v.x; this.y -= v.y; this.z -= v.z; return this; };
    this.multiplyScalar = function(s) { this.x *= s; this.y *= s; this.z *= s; return this; };
    this.normalize = function() { const l = Math.sqrt(this.x*this.x + this.y*this.y + this.z*this.z); if(l > 0) { this.x /= l; this.y /= l; this.z /= l; } return this; };
    this.length = function() { return Math.sqrt(this.x*this.x + this.y*this.y + this.z*this.z); };
    this.distanceTo = function(v) { const dx = this.x - v.x, dy = this.y - v.y, dz = this.z - v.z; return Math.sqrt(dx*dx + dy*dy + dz*dz); };
    this.subVectors = function(a, b) { this.x = a.x - b.x; this.y = a.y - b.y; this.z = a.z - b.z; return this; };
    this.project = function(camera) { return this; }; // 簡化版
};

// 基本場景
THREE.Scene = function() {
    this.children = [];
    this.add = function(object) { this.children.push(object); };
    this.remove = function(object) { const index = this.children.indexOf(object); if(index > -1) this.children.splice(index, 1); };
    this.fog = null;
};

// 基本相機
THREE.PerspectiveCamera = function(fov, aspect, near, far) {
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
    this.fov = fov; this.aspect = aspect; this.near = near; this.far = far;
    this.lookAt = function(target) {};
};

// 基本渲染器
THREE.WebGLRenderer = function(params = {}) {
    this.domElement = params.canvas || document.createElement('canvas');
    this.setSize = function(width, height) { this.domElement.width = width; this.domElement.height = height; };
    this.render = function(scene, camera) {
        const ctx = this.domElement.getContext('2d');
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(0, 0, this.domElement.width, this.domElement.height);
        // 簡化渲染 - 顯示"3D模式不可用"
        ctx.fillStyle = 'white';
        ctx.font = '24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('3D模式載入中...', this.domElement.width/2, this.domElement.height/2);
        ctx.font = '16px Arial';
        ctx.fillText('請使用離線版遊戲', this.domElement.width/2, this.domElement.height/2 + 40);
    };
    this.setClearColor = function() {};
    this.setPixelRatio = function() {};
};

// 基本幾何體
THREE.BoxGeometry = function(w=1, h=1, d=1) { this.type = 'BoxGeometry'; };
THREE.SphereGeometry = function(r=1, ws=8, hs=6) { this.type = 'SphereGeometry'; };
THREE.PlaneGeometry = function(w=1, h=1) { this.type = 'PlaneGeometry'; };
THREE.CylinderGeometry = function(rt=1, rb=1, h=1, rs=8) { this.type = 'CylinderGeometry'; };
THREE.ConeGeometry = function(r=1, h=1, rs=8) { this.type = 'ConeGeometry'; };

// 基本材質
THREE.MeshBasicMaterial = function(params = {}) { this.color = params.color || 0xffffff; this.transparent = params.transparent; this.opacity = params.opacity || 1; };
THREE.MeshLambertMaterial = function(params = {}) { this.color = params.color || 0xffffff; };

// 基本網格
THREE.Mesh = function(geometry, material) {
    this.geometry = geometry; this.material = material;
    this.position = new THREE.Vector3(); this.rotation = new THREE.Vector3(); this.scale = new THREE.Vector3(1,1,1);
    this.add = function(object) {}; this.remove = function(object) {};
    this.lookAt = function(target) {};
};

// 基本組
THREE.Group = function() {
    this.children = []; this.position = new THREE.Vector3(); this.rotation = new THREE.Vector3(); this.scale = new THREE.Vector3(1,1,1);
    this.add = function(object) { this.children.push(object); }; this.remove = function(object) { const i = this.children.indexOf(object); if(i > -1) this.children.splice(i, 1); };
};

// 基本光照
THREE.AmbientLight = function(color, intensity) { this.color = color; this.intensity = intensity; };
THREE.DirectionalLight = function(color, intensity) { this.color = color; this.intensity = intensity; this.position = new THREE.Vector3(); };

// 基本霧
THREE.Fog = function(color, near, far) { this.color = color; this.near = near; this.far = far; };

// 基本顏色
THREE.Color = function(color) { 
    this.r = 1; this.g = 1; this.b = 1;
    this.setHex = function(hex) { return this; };
    this.setHSL = function(h, s, l) { return this; };
};

// 基本時鐘
THREE.Clock = function() {
    this.startTime = Date.now();
    this.getDelta = function() { const now = Date.now(); const delta = (now - this.startTime) / 1000; this.startTime = now; return Math.min(delta, 0.1); };
};

// 常量
THREE.REVISION = '150-mini';

console.log('📦 簡化版Three.js已載入 (版本: ' + THREE.REVISION + ')');
console.log('⚠️ 這是功能受限的版本，建議使用離線版遊戲');
'''
    
    # 保存簡化版Three.js
    with open("js/three.min.js", "w", encoding="utf-8") as f:
        f.write(mini_threejs)
    
    print("✅ 簡化版Three.js已創建")
    return True

def create_fixed_game():
    """創建修復版遊戲"""
    
    print("🔧 創建修復版遊戲...")
    
    try:
        # 讀取原始HTML
        with open("index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        
        # 替換CDN為本地文件
        html_content = html_content.replace(
            'src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"',
            'src="js/three.min.js"'
        )
        
        # 添加錯誤處理
        error_handler = '''
    <script>
        // Three.js載入檢查
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (typeof THREE === 'undefined') {
                    document.body.innerHTML = `
                        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                                    background: #000; color: white; display: flex; flex-direction: column; 
                                    justify-content: center; align-items: center; font-family: Arial;">
                            <h1>🚫 3D版本載入失敗</h1>
                            <p style="font-size: 18px; margin: 20px;">Three.js庫無法載入</p>
                            <div style="margin: 20px;">
                                <button onclick="window.location.href='game_offline.html'" 
                                        style="padding: 15px 30px; font-size: 18px; background: #4CAF50; 
                                               color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px;">
                                    🎮 使用離線版遊戲
                                </button>
                                <button onclick="location.reload()" 
                                        style="padding: 15px 30px; font-size: 18px; background: #2196F3; 
                                               color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px;">
                                    🔄 重新載入
                                </button>
                            </div>
                            <p style="color: #888; margin-top: 30px;">建議使用離線版獲得最佳體驗</p>
                        </div>
                    `;
                }
            }, 2000);
        });
    </script>
'''
        
        # 在</body>前插入錯誤處理
        html_content = html_content.replace('</body>', error_handler + '</body>')
        
        # 保存修復版
        with open("game_3d_fixed.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        print("✅ 修復版遊戲已創建: game_3d_fixed.html")
        return True
        
    except Exception as e:
        print(f"❌ 創建修復版失敗: {e}")
        return False

def main():
    print("🔧 Three.js載入問題修復工具")
    print("=" * 40)
    
    success = False
    
    # 方法1: 嘗試下載Three.js
    print("\n📥 方法1: 下載Three.js到本地")
    if download_threejs_alternative():
        success = True
    
    # 方法2: 創建簡化版
    if not success:
        print("\n🔧 方法2: 創建簡化版Three.js")
        create_embedded_threejs()
    
    # 創建修復版遊戲
    create_fixed_game()
    
    print("\n🎉 修復完成！")
    print("\n📋 現在你有以下遊戲版本:")
    print("1. game_3d_fixed.html - 修復版3D遊戲")
    print("2. game_offline.html - 離線版2D遊戲")
    print("3. index.html - 原版（可能有問題）")
    print("\n💡 建議優先使用 game_offline.html 獲得最佳體驗")

if __name__ == "__main__":
    main()
