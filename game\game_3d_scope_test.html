<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 3D遊戲瞄準鏡測試</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
            overflow: hidden;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 4px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 15%; }
        .scope-mark.bottom { bottom: 15%; }
        .scope-mark.left { left: 15%; }
        .scope-mark.right { right: 15%; }

        /* 瞄準模式下的效果 */
        body.scoped #crosshair {
            opacity: 0.3;
        }

        body.scoped {
            background: #000;
        }

        /* 狀態顯示 */
        #status {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            z-index: 300;
            max-width: 300px;
        }

        .status-item {
            margin: 5px 0;
        }

        .active-status {
            color: #00ff00;
        }

        .inactive-status {
            color: #ff6666;
        }

        #startButton {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px 40px;
            font-size: 24px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            z-index: 400;
        }

        #startButton:hover {
            background: #45a049;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="crosshair"></div>
        
        <!-- 瞄準鏡 -->
        <div id="scope">
            <div class="scope-marks">
                <div class="scope-mark horizontal top"></div>
                <div class="scope-mark horizontal bottom"></div>
                <div class="scope-mark vertical left"></div>
                <div class="scope-mark vertical right"></div>
            </div>
        </div>
        
        <!-- 狀態顯示 -->
        <div id="status">
            <h3>🔍 3D瞄準鏡測試</h3>
            <div class="status-item">Three.js: <span id="threejsStatus" class="inactive-status">載入中...</span></div>
            <div class="status-item">右鍵狀態: <span id="rightClickStatus" class="inactive-status">未按下</span></div>
            <div class="status-item">瞄準鏡: <span id="scopeStatus" class="inactive-status">關閉</span></div>
            <div class="status-item">鼠標鎖定: <span id="lockStatus" class="inactive-status">未鎖定</span></div>
            <div style="margin-top: 15px; font-size: 12px; color: #ccc;">
                <p>• 點擊開始按鈕進入遊戲</p>
                <p>• 右鍵按住 = 開啟瞄準鏡</p>
                <p>• 左鍵點擊 = 射擊</p>
                <p>• 移動鼠標 = 控制視角</p>
            </div>
        </div>
        
        <button id="startButton">🎮 開始測試</button>
    </div>

    <!-- Three.js 庫 -->
    <script src="js/three.min.js"></script>
    
    <script>
        class ScopeTest {
            constructor() {
                this.isScoped = false;
                this.isPointerLocked = false;
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                
                this.init();
            }
            
            init() {
                // 檢查Three.js
                if (typeof THREE !== 'undefined') {
                    document.getElementById('threejsStatus').textContent = '已載入';
                    document.getElementById('threejsStatus').className = 'active-status';
                    console.log('✅ Three.js載入成功');
                } else {
                    document.getElementById('threejsStatus').textContent = '載入失敗';
                    document.getElementById('threejsStatus').className = 'inactive-status';
                    console.log('❌ Three.js載入失敗');
                    return;
                }
                
                this.setupEventListeners();
                this.setupThreeJS();
            }
            
            setupEventListeners() {
                // 開始按鈕
                document.getElementById('startButton').addEventListener('click', () => {
                    this.startTest();
                });
                
                // 鼠標事件
                document.addEventListener('mousedown', (e) => {
                    if (this.isPointerLocked) {
                        if (e.button === 2) { // 右鍵
                            this.toggleScope(true);
                            document.getElementById('rightClickStatus').textContent = '按下';
                            document.getElementById('rightClickStatus').className = 'active-status';
                        } else if (e.button === 0) { // 左鍵
                            console.log('🔫 射擊！');
                        }
                    }
                    e.preventDefault();
                });
                
                document.addEventListener('mouseup', (e) => {
                    if (this.isPointerLocked) {
                        if (e.button === 2) { // 右鍵
                            this.toggleScope(false);
                            document.getElementById('rightClickStatus').textContent = '未按下';
                            document.getElementById('rightClickStatus').className = 'inactive-status';
                        }
                    }
                });
                
                // 禁用右鍵菜單
                document.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                });
                
                // 指針鎖定事件
                document.addEventListener('pointerlockchange', () => {
                    this.isPointerLocked = document.pointerLockElement === document.body;
                    const lockStatus = document.getElementById('lockStatus');
                    if (this.isPointerLocked) {
                        lockStatus.textContent = '已鎖定';
                        lockStatus.className = 'active-status';
                    } else {
                        lockStatus.textContent = '未鎖定';
                        lockStatus.className = 'inactive-status';
                    }
                });
            }
            
            setupThreeJS() {
                const canvas = document.getElementById('gameCanvas');
                
                // 創建場景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);
                
                // 創建相機
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(0, 5, 10);
                
                // 創建渲染器
                this.renderer = new THREE.WebGLRenderer({ canvas: canvas });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                
                // 添加一些測試對象
                this.createTestObjects();
                
                // 開始渲染循環
                this.animate();
                
                console.log('✅ Three.js場景初始化完成');
            }
            
            createTestObjects() {
                // 添加地面
                const groundGeometry = new THREE.PlaneGeometry(100, 100);
                const groundMaterial = new THREE.MeshBasicMaterial({ color: 0x90EE90 });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                this.scene.add(ground);
                
                // 添加一些立方體作為目標
                for (let i = 0; i < 5; i++) {
                    const geometry = new THREE.BoxGeometry(2, 2, 2);
                    const material = new THREE.MeshBasicMaterial({ color: Math.random() * 0xffffff });
                    const cube = new THREE.Mesh(geometry, material);
                    cube.position.set(
                        (Math.random() - 0.5) * 50,
                        1,
                        (Math.random() - 0.5) * 50
                    );
                    this.scene.add(cube);
                }
            }
            
            toggleScope(enable) {
                this.isScoped = enable;
                const scope = document.getElementById('scope');
                const body = document.body;
                const scopeStatus = document.getElementById('scopeStatus');
                
                if (enable) {
                    scope.classList.add('active');
                    body.classList.add('scoped');
                    scopeStatus.textContent = '開啟';
                    scopeStatus.className = 'active-status';
                    console.log('🔍 瞄準鏡啟動');
                } else {
                    scope.classList.remove('active');
                    body.classList.remove('scoped');
                    scopeStatus.textContent = '關閉';
                    scopeStatus.className = 'inactive-status';
                    console.log('🔍 瞄準鏡關閉');
                }
            }
            
            startTest() {
                document.getElementById('startButton').classList.add('hidden');
                document.body.requestPointerLock();
                console.log('🎮 開始瞄準鏡測試');
            }
            
            animate() {
                requestAnimationFrame(() => this.animate());
                
                if (this.renderer && this.scene && this.camera) {
                    this.renderer.render(this.scene, this.camera);
                }
            }
        }
        
        // 啟動測試
        window.addEventListener('load', () => {
            new ScopeTest();
        });
    </script>
</body>
</html>
