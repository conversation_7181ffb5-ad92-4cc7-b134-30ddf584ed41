@echo off
chcp 65001 >nul
title 快速啟動遊戲

echo.
echo ========================================
echo 🎮 末日生存射擊遊戲 - 快速啟動
echo ========================================
echo.

echo 🚀 正在啟動遊戲...

REM 嘗試方法1: 使用Python內建服務器
echo 📝 嘗試方法1: Python HTTP服務器
python -m http.server 8000 >nul 2>&1 &
if %errorlevel% equ 0 (
    echo ✅ Python服務器啟動成功
    echo 🌐 遊戲地址: http://localhost:8000
    timeout /t 2 >nul
    start http://localhost:8000
    goto :success
)

REM 嘗試方法2: 使用Node.js服務器
echo 📝 嘗試方法2: Node.js服務器
node server.js >nul 2>&1 &
if %errorlevel% equ 0 (
    echo ✅ Node.js服務器啟動成功
    echo 🌐 遊戲地址: http://localhost:8000
    timeout /t 2 >nul
    start http://localhost:8000
    goto :success
)

REM 嘗試方法3: 直接打開HTML文件
echo 📝 嘗試方法3: 直接打開HTML文件
if exist "index.html" (
    echo ⚠️ 使用直接文件訪問（可能有限制）
    start index.html
    echo ✅ 遊戲已在瀏覽器中打開
    goto :success
) else (
    echo ❌ 未找到 index.html 文件
    goto :error
)

:success
echo.
echo ========================================
echo 🎉 遊戲啟動成功！
echo ========================================
echo.
echo 💡 遊戲操作說明:
echo    • WASD - 移動
echo    • 鼠標 - 控制視角
echo    • 左鍵 - 射擊
echo    • 空格 - 跳躍
echo    • R - 重新裝彈
echo    • Q - 切換武器
echo    • Tab - 武器商店
echo.
echo 📝 如果遊戲無法正常運行，請:
echo    1. 檢查瀏覽器控制台錯誤信息
echo    2. 確保網絡連接正常
echo    3. 嘗試使用 Chrome 瀏覽器
echo.
echo 🛑 按任意鍵關閉此窗口...
pause >nul
exit /b 0

:error
echo.
echo ========================================
echo ❌ 遊戲啟動失敗
echo ========================================
echo.
echo 💡 請檢查:
echo    1. 確保在遊戲目錄中運行此腳本
echo    2. 安裝 Python 或 Node.js
echo    3. 檢查文件是否完整
echo.
echo 🛑 按任意鍵關閉此窗口...
pause >nul
exit /b 1
