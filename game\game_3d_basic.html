<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧟 末日生存射擊遊戲 - 3D基本版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
            overflow: hidden;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        /* 載入畫面 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            z-index: 1000;
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 4px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 15%; }
        .scope-mark.bottom { bottom: 15%; }
        .scope-mark.left { left: 15%; }
        .scope-mark.right { right: 15%; }

        /* 瞄準模式下的效果 */
        body.scoped #crosshair {
            opacity: 0.3;
        }

        body.scoped {
            background: #000;
        }

        /* 血量條 */
        #healthBar {
            position: absolute;
            bottom: 20px;
            left: 20px;
            width: 200px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            border-radius: 10px;
            overflow: hidden;
        }

        #healthFill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ffff00, #00ff00);
            transition: width 0.3s ease;
            width: 100%;
        }

        /* 彈藥顯示 */
        #ammoDisplay {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* 分數顯示 */
        #scoreDisplay {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* 開始菜單 */
        #startMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #startMenu h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .menu-info {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            line-height: 1.6;
        }

        #startButton {
            padding: 15px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            pointer-events: auto;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        #startButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .hidden {
            display: none !important;
        }

        /* 狀態顯示 */
        #statusDisplay {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            text-align: right;
            z-index: 300;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- 載入畫面 -->
        <div id="loading">
            <div>🎮 載入3D遊戲中...</div>
            <div style="margin-top: 20px; font-size: 16px;">檢查Three.js和遊戲文件...</div>
            <div id="loadingStatus" style="margin-top: 10px; font-size: 14px; color: #ccc;"></div>
        </div>
        
        <!-- 十字準心 -->
        <div id="crosshair"></div>
        
        <!-- 瞄準鏡 -->
        <div id="scope">
            <div class="scope-marks">
                <div class="scope-mark horizontal top"></div>
                <div class="scope-mark horizontal bottom"></div>
                <div class="scope-mark vertical left"></div>
                <div class="scope-mark vertical right"></div>
            </div>
        </div>
        
        <!-- 血量條 -->
        <div id="healthBar">
            <div id="healthFill"></div>
        </div>
        
        <!-- 彈藥顯示 -->
        <div id="ammoDisplay">30/30</div>
        
        <!-- 分數顯示 -->
        <div id="scoreDisplay">分數: 0</div>
        
        <!-- 狀態顯示 -->
        <div id="statusDisplay">
            <div>🔍 瞄準: <span id="scopeStatus">關閉</span></div>
            <div>🎮 狀態: <span id="gameStatus">載入中</span></div>
        </div>
        
        <!-- 開始菜單 -->
        <div id="startMenu">
            <h1>🧟 末日生存射擊</h1>
            <div class="menu-info">
                <p><strong>🎮 遊戲說明：</strong></p>
                <p>• WASD 移動，空格鍵跳躍，鼠標控制視角</p>
                <p>• <strong style="color: #ff6b6b;">左鍵射擊</strong>，<strong style="color: #4ecdc4;">右鍵按住開啟瞄準鏡</strong></p>
                <p>• R 重新裝彈，Q 切換武器</p>
                <p>• 消滅喪屍獲得分數，解鎖更強武器</p>
                <br>
                <p><strong>🔍 瞄準鏡功能：</strong></p>
                <p style="color: #4ecdc4;">• 右鍵按住開啟瞄準鏡</p>
                <p>• 提高射擊精度和傷害</p>
                <p>• 降低鼠標靈敏度便於精確瞄準</p>
            </div>
            <button id="startButton">🚀 開始遊戲</button>
        </div>
    </div>

    <!-- Three.js 庫 -->
    <script src="js/three.min.js"></script>
    
    <!-- 遊戲主程式 -->
    <script src="js/game.js"></script>
    
    <!-- 基本瞄準鏡腳本 -->
    <script>
        // 載入狀態管理
        function updateLoadingStatus(message) {
            const statusElement = document.getElementById('loadingStatus');
            if (statusElement) {
                statusElement.textContent = message;
            }
            console.log('📝 ' + message);
        }
        
        // 檢查依賴
        function checkDependencies() {
            updateLoadingStatus('檢查Three.js...');
            
            if (typeof THREE === 'undefined') {
                updateLoadingStatus('❌ Three.js載入失敗');
                setTimeout(() => {
                    document.getElementById('loading').innerHTML = `
                        <div style="text-align: center;">
                            <h2>❌ Three.js載入失敗</h2>
                            <p>無法載入3D圖形庫</p>
                            <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; font-size: 16px; background: #ff6b6b; color: white; border: none; border-radius: 5px; cursor: pointer;">重新載入</button>
                            <br><br>
                            <a href="game_offline.html" style="color: #4ecdc4; text-decoration: none;">🎮 使用離線版遊戲</a>
                        </div>
                    `;
                }, 2000);
                return false;
            }
            
            updateLoadingStatus('✅ Three.js載入成功');
            return true;
        }
        
        // 瞄準鏡功能
        let scopeActive = false;
        
        function toggleScope(enable) {
            scopeActive = enable;
            const scope = document.getElementById('scope');
            const body = document.body;
            const statusElement = document.getElementById('scopeStatus');
            
            if (enable) {
                scope.classList.add('active');
                body.classList.add('scoped');
                if (statusElement) {
                    statusElement.textContent = '開啟';
                    statusElement.style.color = '#00ff00';
                }
                console.log('🔍 瞄準鏡開啟');
            } else {
                scope.classList.remove('active');
                body.classList.remove('scoped');
                if (statusElement) {
                    statusElement.textContent = '關閉';
                    statusElement.style.color = '#ffffff';
                }
                console.log('🔍 瞄準鏡關閉');
            }
            
            // 如果遊戲實例存在，更新遊戲狀態
            if (window.game && window.game.controls) {
                window.game.controls.isScoped = enable;
                window.game.scopedSensitivity = enable ? 0.0008 : null;
            }
        }
        
        // 等待遊戲載入
        function waitForGame() {
            if (window.game) {
                updateLoadingStatus('✅ 遊戲載入完成');
                document.getElementById('gameStatus').textContent = '已載入';
                
                // 添加瞄準鏡功能到遊戲
                window.game.toggleScope = toggleScope;
                
                // 隱藏載入畫面
                setTimeout(() => {
                    document.getElementById('loading').style.display = 'none';
                }, 1000);
                
                console.log('🎮 遊戲已準備就緒，瞄準鏡功能已添加');
            } else {
                updateLoadingStatus('⏳ 等待遊戲初始化...');
                setTimeout(waitForGame, 1000);
            }
        }
        
        // 初始化
        window.addEventListener('load', function() {
            console.log('🎮 載入基本3D版本...');
            
            if (checkDependencies()) {
                updateLoadingStatus('等待遊戲初始化...');
                waitForGame();
            }
        });
        
        // 錯誤處理
        window.addEventListener('error', function(e) {
            console.error('❌ 載入錯誤:', e.error);
            updateLoadingStatus('❌ 載入錯誤: ' + e.message);
        });
    </script>
</body>
</html>
