<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖱️ 鼠標事件測試</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: white;
            overflow: hidden;
        }

        #testArea {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            cursor: crosshair;
        }

        #eventLog {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            font-size: 14px;
            max-width: 400px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 300;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }

        .left-click {
            background: rgba(255, 0, 0, 0.3);
            border-left: 3px solid #ff0000;
        }

        .right-click {
            background: rgba(0, 255, 0, 0.3);
            border-left: 3px solid #00ff00;
        }

        .mouse-move {
            background: rgba(0, 0, 255, 0.3);
            border-left: 3px solid #0000ff;
        }

        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }

        #clearBtn {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        #clearBtn:hover {
            background: #ff5252;
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            border: 3px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.3);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }
    </style>
</head>
<body>
    <div id="testArea">
        <!-- 瞄準鏡 -->
        <div id="scope"></div>
        
        <!-- 事件日誌 -->
        <div id="eventLog">
            <h3>🖱️ 鼠標事件日誌</h3>
            <div id="logContent"></div>
        </div>
        
        <!-- 清除按鈕 -->
        <button id="clearBtn" onclick="clearLog()">清除日誌</button>
        
        <!-- 說明 -->
        <div id="instructions">
            <h4>📝 測試說明</h4>
            <p>• 左鍵 = 射擊 (紅色)</p>
            <p>• 右鍵按住 = 瞄準鏡 (綠色)</p>
            <p>• 移動鼠標 = 視角控制 (藍色)</p>
            <p>• 觀察日誌確認事件正確觸發</p>
        </div>
    </div>

    <script>
        let eventCount = 0;
        let isScoped = false;
        
        function addLogEntry(message, type) {
            eventCount++;
            const logContent = document.getElementById('logContent');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `<strong>#${eventCount}</strong> ${message} <small>(${new Date().toLocaleTimeString()})</small>`;
            logContent.appendChild(entry);
            
            // 自動滾動到底部
            logContent.scrollTop = logContent.scrollHeight;
            
            // 限制日誌條目數量
            if (logContent.children.length > 50) {
                logContent.removeChild(logContent.firstChild);
            }
        }
        
        function toggleScope(enable) {
            isScoped = enable;
            const scope = document.getElementById('scope');
            
            if (enable) {
                scope.classList.add('active');
                addLogEntry('🔍 瞄準鏡開啟', 'right-click');
            } else {
                scope.classList.remove('active');
                addLogEntry('🔍 瞄準鏡關閉', 'right-click');
            }
        }
        
        function clearLog() {
            document.getElementById('logContent').innerHTML = '';
            eventCount = 0;
            addLogEntry('📝 日誌已清除', 'mouse-move');
        }
        
        // 鼠標事件監聽
        document.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // 左鍵
                addLogEntry('🔫 左鍵按下 - 射擊', 'left-click');
            } else if (e.button === 2) { // 右鍵
                toggleScope(true);
            }
            e.preventDefault();
        });
        
        document.addEventListener('mouseup', (e) => {
            if (e.button === 0) { // 左鍵
                addLogEntry('🔫 左鍵釋放', 'left-click');
            } else if (e.button === 2) { // 右鍵
                toggleScope(false);
            }
        });
        
        document.addEventListener('click', (e) => {
            if (e.button === 0) {
                addLogEntry('🖱️ Click事件 (左鍵)', 'left-click');
            } else if (e.button === 2) {
                addLogEntry('🖱️ Click事件 (右鍵) - 應該被阻止', 'right-click');
            }
        });
        
        // 禁用右鍵菜單
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            addLogEntry('🚫 右鍵菜單被阻止', 'right-click');
        });
        
        // 鼠標移動（節流）
        let mouseMoveThrottle = false;
        document.addEventListener('mousemove', (e) => {
            if (!mouseMoveThrottle) {
                mouseMoveThrottle = true;
                setTimeout(() => {
                    addLogEntry(`🖱️ 鼠標移動 (${e.clientX}, ${e.clientY})`, 'mouse-move');
                    mouseMoveThrottle = false;
                }, 100);
            }
        });
        
        // 初始化
        window.addEventListener('load', () => {
            addLogEntry('🎮 鼠標事件測試頁面載入完成', 'mouse-move');
            addLogEntry('📝 開始測試左鍵射擊和右鍵瞄準', 'mouse-move');
        });
    </script>
</body>
</html>
