#!/usr/bin/env python3
"""
下載Three.js庫到本地，解決網絡依賴問題
"""

import urllib.request
import os
import sys

def download_threejs():
    """下載Three.js庫到本地"""
    
    # Three.js CDN URL
    threejs_url = "https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"
    
    # 本地保存路徑
    local_path = "js/three.min.js"
    
    print("🔄 正在下載 Three.js 庫...")
    print(f"📥 來源: {threejs_url}")
    print(f"💾 保存到: {local_path}")
    
    try:
        # 確保js目錄存在
        os.makedirs("js", exist_ok=True)
        
        # 下載文件
        urllib.request.urlretrieve(threejs_url, local_path)
        
        # 檢查文件大小
        file_size = os.path.getsize(local_path)
        print(f"✅ 下載完成！文件大小: {file_size:,} 字節")
        
        # 創建修復版的HTML文件
        create_fixed_html()
        
        return True
        
    except Exception as e:
        print(f"❌ 下載失敗: {e}")
        print("💡 請檢查網絡連接或手動下載Three.js")
        return False

def create_fixed_html():
    """創建使用本地Three.js的修復版HTML"""
    
    print("🔧 創建修復版遊戲文件...")
    
    # 讀取原始HTML
    try:
        with open("index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        
        # 替換CDN鏈接為本地文件
        html_content = html_content.replace(
            'src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"',
            'src="js/three.min.js"'
        )
        
        # 保存修復版
        with open("game_fixed.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        print("✅ 修復版遊戲已創建: game_fixed.html")
        print("🎮 現在可以完全離線運行3D版本的遊戲了！")
        
    except Exception as e:
        print(f"❌ 創建修復版失敗: {e}")

def main():
    print("🎮 Three.js 本地化工具")
    print("=" * 40)
    
    # 檢查是否已存在本地Three.js
    if os.path.exists("js/three.min.js"):
        file_size = os.path.getsize("js/three.min.js")
        print(f"✅ 本地Three.js已存在 (大小: {file_size:,} 字節)")
        
        choice = input("是否重新下載？(y/N): ").lower()
        if choice != 'y':
            create_fixed_html()
            return
    
    # 下載Three.js
    if download_threejs():
        print("\n🎉 完成！現在你有兩個版本的遊戲:")
        print("1. game_offline.html - 2D簡化版（完全離線）")
        print("2. game_fixed.html - 3D完整版（使用本地Three.js）")
        print("\n💡 建議先嘗試 game_fixed.html 獲得完整體驗")
    else:
        print("\n⚠️ 如果無法下載，請使用 game_offline.html")

if __name__ == "__main__":
    main()
