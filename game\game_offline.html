<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧟 末日生存射擊遊戲 (離線版)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            overflow: hidden;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 50%, #8FBC8F 100%);
        }

        #gameCanvas {
            width: 100%;
            height: 100%;
            background: #000;
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
            transition: all 0.3s ease;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            border: 3px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.3);
            pointer-events: none;
            z-index: 150;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 1px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 1px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 20px;
            height: 1px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 1px;
            height: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 20%; }
        .scope-mark.bottom { bottom: 20%; }
        .scope-mark.left { left: 20%; }
        .scope-mark.right { right: 20%; }

        /* 瞄準模式下的十字準心 */
        #crosshair.scoped {
            opacity: 0;
        }

        /* 瞄準模式下的視野效果 */
        body.scoped {
            background: #000;
        }

        body.scoped #gameContainer {
            transform: scale(1.5);
            transition: transform 0.3s ease;
        }

        /* HUD */
        #hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 50;
        }

        #stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            pointer-events: auto;
        }

        #gameStatus {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            text-align: right;
        }

        /* 開始菜單 */
        #startMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #startMenu h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .menu-info {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            line-height: 1.6;
        }

        #startButton {
            padding: 15px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            pointer-events: auto;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        #startButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        /* 遊戲區域 */
        .game-world {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
        }

        .building {
            position: absolute;
            background: #666;
            border: 2px solid #444;
        }

        .zombie {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #8B4513;
            border-radius: 50%;
            border: 2px solid #654321;
            transition: all 0.1s;
        }

        .zombie::before {
            content: '🧟';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 20px;
        }

        .bullet {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffff00;
            border-radius: 50%;
            box-shadow: 0 0 5px #ffff00;
        }

        .hidden {
            display: none !important;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            #startMenu h1 {
                font-size: 36px;
            }
            
            .menu-info {
                font-size: 16px;
                padding: 0 20px;
            }
            
            #startButton {
                font-size: 20px;
                padding: 12px 30px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <!-- HUD -->
        <div id="hud">
            <div id="crosshair"></div>

            <!-- 瞄準鏡 -->
            <div id="scope">
                <div class="scope-marks">
                    <div class="scope-mark horizontal top"></div>
                    <div class="scope-mark horizontal bottom"></div>
                    <div class="scope-mark vertical left"></div>
                    <div class="scope-mark vertical right"></div>
                </div>
            </div>
            
            <div id="gameStatus">
                <div>分數: <span id="scoreValue">0</span></div>
                <div>🔫 <span id="weaponInfo">手槍</span></div>
                <div>敵人: <span id="enemyCount">0</span></div>
                <div>FPS: <span id="fpsDisplay">60</span></div>
            </div>
            
            <div id="stats">
                <div style="color: #ff4444;">❤️ 生命值: <span id="healthValue">100</span></div>
                <div style="color: #44ff44;">🔫 彈藥: <span id="ammoValue">12/12</span></div>
                <div style="color: #44ffff;">🔧 廢料: <span id="scrapValue">0</span> | 💊 醫療包: <span id="medkitValue">0</span></div>
            </div>
        </div>
        
        <!-- 開始菜單 -->
        <div id="startMenu">
            <h1>🧟 末日生存射擊</h1>
            <div class="menu-info">
                <p><strong>🎮 遊戲說明：</strong></p>
                <p>• WASD 移動，鼠標控制視角，左鍵射擊</p>
                <p>• 右鍵按住開啟瞄準鏡（提高精度和傷害）</p>
                <p>• R 重新裝彈，Q 切換武器</p>
                <p>• 消滅喪屍獲得分數，收集資源生存</p>
                <br>
                <p><strong>🔫 武器解鎖：</strong></p>
                <p>🔫 手槍(0分) → 💥 霰彈槍(500分) → 🔥 突擊步槍(1500分) → ⚡ 狙擊槍(3000分)</p>
                <br>
                <p style="color: #ffff44;"><strong>⚠️ 離線簡化版本</strong></p>
                <p>此版本不需要網絡連接，使用2D圖形渲染</p>
            </div>
            <button id="startButton">🚀 開始遊戲</button>
        </div>
    </div>

    <script>
        // 簡化的2D遊戲引擎
        class OfflineGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.gameState = 'menu';
                
                // 遊戲狀態
                this.score = 0;
                this.health = 100;
                this.currentWeaponIndex = 0;
                this.resources = { scrap: 0, medkits: 0 };
                
                // 武器系統
                this.weapons = [
                    { name: '手槍', icon: '🔫', damage: 35, maxAmmo: 12, currentAmmo: 12, unlockScore: 0 },
                    { name: '霰彈槍', icon: '💥', damage: 80, maxAmmo: 6, currentAmmo: 6, unlockScore: 500 },
                    { name: '突擊步槍', icon: '🔥', damage: 45, maxAmmo: 30, currentAmmo: 30, unlockScore: 1500 },
                    { name: '狙擊槍', icon: '⚡', damage: 150, maxAmmo: 5, currentAmmo: 5, unlockScore: 3000 }
                ];
                
                // 遊戲對象
                this.player = { x: 400, y: 300, angle: 0 };
                this.zombies = [];
                this.bullets = [];
                this.buildings = [];
                
                // 控制
                this.keys = {};
                this.mouse = { x: 0, y: 0, down: false, rightDown: false };
                this.isScoped = false;
                
                // 性能
                this.fps = 60;
                this.lastTime = 0;
                this.frameCount = 0;
                this.lastFPSTime = 0;
                
                this.init();
            }
            
            init() {
                this.setupCanvas();
                this.setupEventListeners();
                this.createBuildings();
                this.updateUI();
                
                console.log('🎮 離線遊戲初始化完成');
            }
            
            setupCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
                
                window.addEventListener('resize', () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                });
            }
            
            setupEventListeners() {
                // 開始按鈕
                document.getElementById('startButton').addEventListener('click', () => {
                    this.startGame();
                });
                
                // 鍵盤事件
                document.addEventListener('keydown', (e) => {
                    this.keys[e.code] = true;
                    
                    if (this.gameState === 'playing') {
                        switch(e.code) {
                            case 'KeyR':
                                this.reload();
                                break;
                            case 'KeyQ':
                                this.switchWeapon();
                                break;
                            case 'Digit1':
                                this.selectWeapon(0);
                                break;
                            case 'Digit2':
                                this.selectWeapon(1);
                                break;
                            case 'Digit3':
                                this.selectWeapon(2);
                                break;
                            case 'Digit4':
                                this.selectWeapon(3);
                                break;
                        }
                    }
                });
                
                document.addEventListener('keyup', (e) => {
                    this.keys[e.code] = false;
                });
                
                // 鼠標事件
                this.canvas.addEventListener('mousemove', (e) => {
                    this.mouse.x = e.clientX;
                    this.mouse.y = e.clientY;
                    
                    if (this.gameState === 'playing') {
                        // 計算玩家朝向
                        const centerX = this.canvas.width / 2;
                        const centerY = this.canvas.height / 2;
                        this.player.angle = Math.atan2(e.clientY - centerY, e.clientX - centerX);
                    }
                });
                
                this.canvas.addEventListener('mousedown', (e) => {
                    if (e.button === 0) { // 左鍵
                        this.mouse.down = true;
                        if (this.gameState === 'playing') {
                            this.shoot();
                        }
                    } else if (e.button === 2) { // 右鍵
                        this.mouse.rightDown = true;
                        if (this.gameState === 'playing') {
                            this.toggleScope(true);
                        }
                    }
                    e.preventDefault();
                });

                this.canvas.addEventListener('mouseup', (e) => {
                    if (e.button === 0) { // 左鍵
                        this.mouse.down = false;
                    } else if (e.button === 2) { // 右鍵
                        this.mouse.rightDown = false;
                        if (this.gameState === 'playing') {
                            this.toggleScope(false);
                        }
                    }
                });

                // 禁用右鍵菜單
                this.canvas.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                });
                
                // 鎖定鼠標指針
                this.canvas.addEventListener('click', () => {
                    if (this.gameState === 'playing') {
                        this.canvas.requestPointerLock();
                    }
                });
            }
            
            createBuildings() {
                // 創建一些建築物
                for (let i = 0; i < 10; i++) {
                    this.buildings.push({
                        x: Math.random() * this.canvas.width,
                        y: Math.random() * this.canvas.height,
                        width: 50 + Math.random() * 100,
                        height: 50 + Math.random() * 100,
                        color: `hsl(${Math.random() * 60 + 200}, 30%, 40%)`
                    });
                }
            }
            
            startGame() {
                this.gameState = 'playing';
                document.getElementById('startMenu').classList.add('hidden');
                this.gameLoop();
                
                // 開始生成喪屍
                this.spawnZombies();
                setInterval(() => this.spawnZombies(), 3000);
                
                console.log('🎮 遊戲開始！');
            }
            
            spawnZombies() {
                if (this.gameState !== 'playing' || this.zombies.length >= 8) return;
                
                const zombie = {
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height,
                    health: 50,
                    maxHealth: 50,
                    speed: 30 + Math.random() * 20,
                    type: Math.random() < 0.7 ? 'normal' : (Math.random() < 0.8 ? 'fast' : 'tank')
                };
                
                // 確保不在玩家附近生成
                const playerCenterX = this.canvas.width / 2;
                const playerCenterY = this.canvas.height / 2;
                const distance = Math.sqrt((zombie.x - playerCenterX) ** 2 + (zombie.y - playerCenterY) ** 2);
                
                if (distance > 100) {
                    this.zombies.push(zombie);
                }
            }
            
            shoot() {
                const weapon = this.weapons[this.currentWeaponIndex];
                if (weapon.currentAmmo <= 0) {
                    this.reload();
                    return;
                }

                weapon.currentAmmo--;

                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;

                // 瞄準時精度更高
                let accuracy = this.isScoped ? 0.98 : 0.85;
                let spread = this.isScoped ? 0.02 : 0.1;

                // 添加射擊散布
                const angleSpread = (Math.random() - 0.5) * spread;
                const finalAngle = this.player.angle + angleSpread;

                // 瞄準時子彈速度更快
                const bulletSpeed = this.isScoped ? 700 : 500;

                this.bullets.push({
                    x: centerX,
                    y: centerY,
                    vx: Math.cos(finalAngle) * bulletSpeed,
                    vy: Math.sin(finalAngle) * bulletSpeed,
                    damage: this.isScoped ? weapon.damage * 1.2 : weapon.damage, // 瞄準時傷害加成
                    life: 3
                });

                const scopeText = this.isScoped ? ' (瞄準)' : '';
                console.log(`🔫 ${weapon.icon} 射擊${scopeText}！剩餘彈藥: ${weapon.currentAmmo}`);
                this.updateUI();
            }
            
            reload() {
                const weapon = this.weapons[this.currentWeaponIndex];
                weapon.currentAmmo = weapon.maxAmmo;
                console.log(`🔄 ${weapon.name} 重新裝彈完成`);
                this.updateUI();
            }
            
            switchWeapon() {
                do {
                    this.currentWeaponIndex = (this.currentWeaponIndex + 1) % this.weapons.length;
                } while (this.score < this.weapons[this.currentWeaponIndex].unlockScore);
                
                console.log(`🔄 切換到 ${this.weapons[this.currentWeaponIndex].icon} ${this.weapons[this.currentWeaponIndex].name}`);
                this.updateUI();
            }
            
            selectWeapon(index) {
                if (index < this.weapons.length && this.score >= this.weapons[index].unlockScore) {
                    this.currentWeaponIndex = index;
                    console.log(`🎯 選擇 ${this.weapons[index].icon} ${this.weapons[index].name}`);
                    this.updateUI();
                }
            }

            toggleScope(enable) {
                this.isScoped = enable;
                const scope = document.getElementById('scope');
                const crosshair = document.getElementById('crosshair');
                const body = document.body;

                if (enable) {
                    scope.classList.add('active');
                    crosshair.classList.add('scoped');
                    body.classList.add('scoped');
                    console.log('🔍 瞄準鏡啟動');
                } else {
                    scope.classList.remove('active');
                    crosshair.classList.remove('scoped');
                    body.classList.remove('scoped');
                    console.log('🔍 瞄準鏡關閉');
                }
            }
            
            update(deltaTime) {
                if (this.gameState !== 'playing') return;
                
                // 更新玩家移動
                this.updatePlayer(deltaTime);
                
                // 更新子彈
                this.updateBullets(deltaTime);
                
                // 更新喪屍
                this.updateZombies(deltaTime);
                
                // 檢查碰撞
                this.checkCollisions();
                
                // 更新UI
                this.updateUI();
            }
            
            updatePlayer(deltaTime) {
                // 玩家移動邏輯（視覺上玩家在中心，實際移動世界）
                // 這裡簡化處理
            }
            
            updateBullets(deltaTime) {
                for (let i = this.bullets.length - 1; i >= 0; i--) {
                    const bullet = this.bullets[i];
                    bullet.x += bullet.vx * deltaTime;
                    bullet.y += bullet.vy * deltaTime;
                    bullet.life -= deltaTime;
                    
                    if (bullet.life <= 0 || 
                        bullet.x < 0 || bullet.x > this.canvas.width ||
                        bullet.y < 0 || bullet.y > this.canvas.height) {
                        this.bullets.splice(i, 1);
                    }
                }
            }
            
            updateZombies(deltaTime) {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                
                for (let zombie of this.zombies) {
                    // 朝玩家移動
                    const dx = centerX - zombie.x;
                    const dy = centerY - zombie.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > 0) {
                        zombie.x += (dx / distance) * zombie.speed * deltaTime;
                        zombie.y += (dy / distance) * zombie.speed * deltaTime;
                    }
                }
            }
            
            checkCollisions() {
                // 子彈與喪屍碰撞
                for (let i = this.bullets.length - 1; i >= 0; i--) {
                    const bullet = this.bullets[i];
                    
                    for (let j = this.zombies.length - 1; j >= 0; j--) {
                        const zombie = this.zombies[j];
                        const distance = Math.sqrt((bullet.x - zombie.x) ** 2 + (bullet.y - zombie.y) ** 2);
                        
                        if (distance < 20) {
                            // 命中
                            zombie.health -= bullet.damage;
                            this.bullets.splice(i, 1);
                            
                            if (zombie.health <= 0) {
                                // 喪屍死亡
                                this.zombies.splice(j, 1);
                                this.score += zombie.type === 'tank' ? 200 : zombie.type === 'fast' ? 150 : 100;
                                console.log(`💀 擊殺喪屍！獲得分數: ${this.score}`);
                            }
                            break;
                        }
                    }
                }
                
                // 喪屍與玩家碰撞
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                
                for (let zombie of this.zombies) {
                    const distance = Math.sqrt((zombie.x - centerX) ** 2 + (zombie.y - centerY) ** 2);
                    if (distance < 30) {
                        this.health -= 10;
                        zombie.x += (Math.random() - 0.5) * 50; // 推開喪屍
                        zombie.y += (Math.random() - 0.5) * 50;
                        
                        if (this.health <= 0) {
                            this.gameOver();
                        }
                    }
                }
            }
            
            render() {
                // 清空畫布
                this.ctx.fillStyle = 'linear-gradient(to bottom, #87CEEB, #98FB98)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 繪製背景
                const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
                gradient.addColorStop(0, '#87CEEB');
                gradient.addColorStop(1, '#98FB98');
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 繪製建築物
                for (let building of this.buildings) {
                    this.ctx.fillStyle = building.color;
                    this.ctx.fillRect(building.x, building.y, building.width, building.height);
                    this.ctx.strokeStyle = '#333';
                    this.ctx.lineWidth = 2;
                    this.ctx.strokeRect(building.x, building.y, building.width, building.height);
                }
                
                // 繪製喪屍
                for (let zombie of this.zombies) {
                    this.ctx.fillStyle = '#8B4513';
                    this.ctx.beginPath();
                    this.ctx.arc(zombie.x, zombie.y, 15, 0, Math.PI * 2);
                    this.ctx.fill();
                    
                    this.ctx.strokeStyle = '#654321';
                    this.ctx.lineWidth = 2;
                    this.ctx.stroke();
                    
                    // 繪製喪屍表情
                    this.ctx.fillStyle = '#ff0000';
                    this.ctx.fillRect(zombie.x - 3, zombie.y - 3, 2, 2);
                    this.ctx.fillRect(zombie.x + 1, zombie.y - 3, 2, 2);
                }
                
                // 繪製子彈
                for (let bullet of this.bullets) {
                    this.ctx.fillStyle = '#ffff00';
                    this.ctx.shadowColor = '#ffff00';
                    this.ctx.shadowBlur = 5;
                    this.ctx.beginPath();
                    this.ctx.arc(bullet.x, bullet.y, 3, 0, Math.PI * 2);
                    this.ctx.fill();
                    this.ctx.shadowBlur = 0;
                }
                
                // 繪製玩家（中心十字）
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 3;
                this.ctx.beginPath();
                this.ctx.moveTo(centerX - 10, centerY);
                this.ctx.lineTo(centerX + 10, centerY);
                this.ctx.moveTo(centerX, centerY - 10);
                this.ctx.lineTo(centerX, centerY + 10);
                this.ctx.stroke();
                
                // 繪製武器方向指示
                const weaponLength = 30;
                const weaponX = centerX + Math.cos(this.player.angle) * weaponLength;
                const weaponY = centerY + Math.sin(this.player.angle) * weaponLength;
                
                this.ctx.strokeStyle = '#ff6b6b';
                this.ctx.lineWidth = 4;
                this.ctx.beginPath();
                this.ctx.moveTo(centerX, centerY);
                this.ctx.lineTo(weaponX, weaponY);
                this.ctx.stroke();
            }
            
            updateUI() {
                document.getElementById('scoreValue').textContent = this.score;
                document.getElementById('healthValue').textContent = Math.max(0, this.health);
                document.getElementById('weaponInfo').textContent = this.weapons[this.currentWeaponIndex].name;
                document.getElementById('ammoValue').textContent = 
                    `${this.weapons[this.currentWeaponIndex].currentAmmo}/${this.weapons[this.currentWeaponIndex].maxAmmo}`;
                document.getElementById('enemyCount').textContent = this.zombies.length;
                document.getElementById('fpsDisplay').textContent = this.fps;
                document.getElementById('scrapValue').textContent = this.resources.scrap;
                document.getElementById('medkitValue').textContent = this.resources.medkits;
            }
            
            updatePerformance() {
                this.frameCount++;
                const currentTime = Date.now();
                
                if (currentTime - this.lastFPSTime >= 1000) {
                    this.fps = this.frameCount;
                    this.frameCount = 0;
                    this.lastFPSTime = currentTime;
                }
            }
            
            gameLoop(currentTime = 0) {
                if (this.gameState !== 'playing') return;
                
                const deltaTime = (currentTime - this.lastTime) / 1000;
                this.lastTime = currentTime;
                
                this.update(Math.min(deltaTime, 0.1)); // 限制deltaTime
                this.render();
                this.updatePerformance();
                
                requestAnimationFrame((time) => this.gameLoop(time));
            }
            
            gameOver() {
                this.gameState = 'gameOver';
                alert(`🎮 遊戲結束！\n最終分數: ${this.score}\n\n點擊確定重新開始`);
                location.reload();
            }
        }

        // 啟動遊戲
        window.addEventListener('load', () => {
            console.log('🎮 載入離線版遊戲...');
            new OfflineGame();
        });
    </script>
</body>
</html>
