
# score01 = 150    # score01 是國文成績
# score02 = 180    # score02 是國文成績

# sum = score01 + score02
# averge = sum / 2
# print("平均:" , averge)
# sum = score02 - score01
# print("相差:", score02 - score01)

# num4 = 8 + True
# print(num4)

# num5 = 8 + False
# print(num5)

# str4 = "大家好! \n歡迎光臨"
# print(str4)

# str4 = "大家好! \t歡迎光臨"
# print(str4)

# str4 = "大家好! \r歡迎光臨"
# print(str4)

# str4 = "大家好大你好! \r歡迎光臨"
# print(str4)

# print(type(str4))

# a = 30.0
# print(type(a))

# a = 50 + 38.6
# print(a)
# print(int(a))
# print(float(a))
# print(str(a))

# a = 38
# b = "40.7"
# print(a + float(b))  # 78.7
# print(str(a)+ b)     # 3840.7

# score = "100"
# print("我的五科成績總分" + score)
# print("我的五科成績總分" + str(score) + "分")
# print("我的身價" , score, "元" , sep = "" ,end= " ") 

# name = "醜男政"
# score = 100
# print("%3s的數學成績為%6.2f" % (name, score))

# name1 = "小王"

# score1 = 99.99

# print("%-4s的數學成績為%6.2f" % (name1, score1))
# print ("{0}的成績為{1}" . format("林小明", 80))

# print ("數學考{1}分的人是{0}" . format("林大明", 90))
# print ("數學考{0:3s}分的人是{1:6.2f}" . format("林大明", 90.00))
# print ("數學考{0:5s}分的人是{1:3d}" . format("林大明", 90.00))
# print ("數學考{:5}分的人是{:9}" . format("林大明", 90.00))


# print("姓名  座號  國文  數學  英文")
# print("%3s %2d %3d %3d %3d" % ("林大明" , 1 , 99 , 99 , 60))
# print("%3s %2d %3d %3d %3d" % ("林中明" , 2 , 60 , 59 , 58)) 
# print("%3s %2d %3d %3d %3d" % ("林小明" , 4 , 50 , 60 , 99))


# name = input("請輸入姓名 : ")
# score01 = input("請輸入數學成績: ")
# print("%s的數學成績是%s分" % (name, int(score01) + 10))

# chinese = int(input("請輸入國文成績: "))
# math = int(input("請輸入數學成績: "))
# english = int(input("請輸入英文成績: "))
# total = chinese + math + english
# print("你的成績總分為:" + str(total))

# salary =float(input("請輸入薪資金額: "))
# bonus =float(input("請輸入工作獎金: "))
# overtime_pay =float(input("請輸入加班費: "))
# total = salary + bonus + overtime_pay
# print("本月實領金額:" + str(total) + ("元"))

# a = 6
# b = 0
# print(a/b)

# top = float(input("請輸入梯形上底長度:"))
# bottom = float(input("請輸入梯形下底長度:"))
# height = float(input("請輸入梯形高度:"))
# area = (top + bottom) * height / 2
# print("梯形的面積為:" + str(area) + ("cm2"))

# long = float(input("請輸入長方形的長度:"))
# high = float(input("請輸入長方形的高度:"))
# area = long * high
# print("長方形的面積為:" + str(area))

# deposit = int(input("請輸入本金存款金額"))
# times = 1.02 ** 6
# deposit  *= times
# print("6 年後存款為:" + str(deposit))

# money = int(input("請輸入你有多少錢:"))
# deposit = int(input("請輸入手機金額:"))
# totle = money - deposit
# print("剩餘存款:" + str(totle)

# height = int(input("請輸入你的身高:"))
# weight = int(input("請輸入你的體重:"))
# BMI = float(weight / ((height/100) ** 2))
# print("身高 %3d, 體重%2d, BMI值為%-5.2f" % (height, weight, BMI))

# pw = input("請輸入密碼:")
# if  pw=="11223344556677889900":
#     print("歡迎光臨!")    

# pw = input("請輸入密碼:")
# if  pw=="11223344556677889900":
#     print("歡迎光臨!")    
# else:
#     print("密碼錯誤!")

# score = int(input("請輸入成績:"))
# if score >= 90:
#     print("優等")
# elif score >= 80:
#     print("甲等")
# elif score >= 70:
#     print("乙等")
# elif score >= 60:
#     print("丙等")
# else:
#     print("丁等")

# score = int(input("請輸入年齡:"))
# if score < 6:
#     print("可看普遍級!")
# elif score < 12:
#     print("可看普遍級及保護級!")
# elif score < 18:
#     print("可看限制級以外的所有影片!")
# else:
#     print("您已成年,可看各級影片!")

# a = int(input("請輸入a的值:"))
# b = int(input("請輸入b的值:"))
# c = int(input("請輸入c的值:"))
# if(a > b):
#     if(c > a):
#         print("最大值為 %d" % c)
#     else:
#         print("最大值為 %d" % a)
# if(b > a):
#     if(c > b):
#         print("最大值為 %d" % c)
#     else:
#         print("最大值為 %d" % b)

# list1 = range(5)
# print(list(list1))

# list1 = range(-3 , 😎
# print(list(list1))

# list1 = range(10)  
# list2 = range(1 , 10) 
# list3 = range(1 , 10 , 2)
# list4 = range(10 , 0 , -2)
# print(list(list1))
# print(list(list2))
# print(list(list3))
# print(list(list4))

# for n in range(5):
# print(n, end = ',')

# n = int(input("請輸入正整數:"))
# for i in range(1, n+1):
#     print(i,end=" ")

# start = int(input("請輸入起始值:"))
# end = int(input("請輸入結束值:"))
# interval = int(input("請輸入間隔值:"))
# for i in range(start , end+1 , interval ):
#     print(i,end=" ")

# sum = 0
# start = int(input("請輸入起始值:"))
# end = int(input("請輸入結束值:"))
# for n in range(start, end+1):
#     sum += n
# print(" %d 到 %d 的整數和為 %d " % (start, end, sum))

# n = 0
# for i in range(1, 5):
#     for j in range(1, 5):
#         n += 1
# print(n)

# for i in range(1, 10):
#     for j in range(1, 10):
#         product = i * j
#         print("%d*%d=%-2d" % (i, j, product), end = " ")
#     print()

# i = int(input("請輸入正整數:"))
# for i in range(1, i+1):
#     for j in range(1, i+1):
#         print(j, end = "")
#     print()

# n = int(input("請輸入正整數:"))
# for i in range(1, n+1):
#     for j in range(n, i-1, -1):
#         print("*", end = "")
#     print()


# for i in range(1, 11):
#     if(i==6):
#         break
#     print(i, end=",")                     maxon

# a = int(input("請輸入a的值:"))
# b = int(input("請輸入b的值:"))
# maxno = a * b
# for i in range(1, maxno+1):
#     if(i % a ==0 and i % b ==0):
#         break
# print("%d 和 %d的最小公倍數為%d" % (a, b, i))
# num = int(input("請輸入一個整數:"))

# is_prime = True
# i = 2
# while i < num:
#     if num % i == 0:
#         is_prime = False
#         break
#     i += 1
# if is_prime:
#     print(num, "是質數")
# else:
#     print(num, "不是質數")

# score = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
# print(score)

# list = ["林大明", "林中明", "林小明"]
# print(list)

# list = [1, "香蕉", "芭樂", 100]
# print(list)

# list = ["香蕉", "芭樂", "蘋果", "桃子"]
# print(list[1:4:2])

# list1 = [1, 2, 3, 4, 5]
# print(list1[0])
# list1[0] = 9
# print(list1[0])

# list1 = ["香蕉", "芭樂", "蘋果", "桃子"]
# for s in list1:
#     print(s, end = ",")

# i = 1
# names = ["林大明", "林中明", "林小明"]
# for name in names:
#     print("編號 %d 姓名: %s " % (i,name))
#     i += 1

# scores = [90, 80]
# for i in range(len(scores)):
#     print(scores[i])

# i = 0
# names = ["林大明", "林中明", "林小明"]
# for name in range(len(names)):
#     i += -1
#     print(names[i])

# list1 = ["香蕉", "芭樂", "蘋果","香蕉"]
# n = list1.index("芭樂")
# print(n)
# m = list1.index("梨子")
# print(m)  
# n = list1.count("香蕉")
# print(n)
# m = list1.count("梨子")
# print(m)

# list1 = [1, 2, 3, 4, 5]
# list1.append(6)
# print(list1)
# print(list1[5])
# print(len(list1))

# list1 = [1, 2, 3, 4, 5]
# list1.insert(3,"紅榜")
# print(list1)
# print(list1[3])
# print(len(list1))

# total = 0
# while True:
#     deposit = int(input("請輸入第%d天的存款金額:" % day))
#     if deposit == 0:
#         break
#     total += deposit

# print("總存款金額:", total)

# scores = []
# total = inscore = 0
# while(inscore!= -1):
#     inscore = int(input("請輸入成績:"))
#     if (inscore!= -1):
#         scores.append(inscore)
# print("共有 %d 位學生" % len(scores))
# for score in scores:
#     total += score
# average = total / len(scores)
# print("本般總成績:%d 分,平均成績:%5.2f 分" % (total, average))

# score = []
# total = inscore = 0
# for i in range(1, 8):
#     inscore = int(input("請輸入第%d天的存款金額:" % i))
#     score.append(inscore)
#     total += inscore
#     if i == 7:
#         print("總存款金額:%d 元" % total)
#         average = total / 7
#         print("平均每天存:%5.2f 元" % average)
    
# list1 = [1, 2, 3, 4, 5]
# list1.remove(3)
# print(list1)

# list1 = [1, 2, 3, 4, 5]
# n = list1.pop()
# print(list1)
# n = list1.pop(2)
# print(list1)

# list1 = [1, 2, 3, 4, 5, 6]
# del list1[1]
# print(list1)
# list2 = [1, 2, 3, 4, 5, 6]
# del list2[1 : 5 : 2]
# print(list2)

# colors = ["紅", "綠", "藍", "黃"]
# while True:
#     print("串列原素有:", colors)
#     color = input("請輸入要刪除的顏色(輸入EXIT結束):")
#     n = colors.count(color)
#     if (n>0):
#         colors.remove(color)
#     if(color=="EXIT"):
#         break
#     else:
#         print(color,"不再串列中!")

# list1 = [3, 2, 1, 5]
# list1.sort()
# print(list1)

# list1 = [3, 4, 1, 2, 5]
# # list1.sort()
# # list1.reverse()
# list1.sort(reverse = True)
# print(list1)

# list1 = [3, 4, 1, 2, 5]
# list2 = sorted(list1 , reverse = True)
# list2 = sorted(list1 , reverse = False)
# print(list1)
# print(list2)

# scores = []
# while True:
#     inscore = int(input("請輸入成績:"))
#     if (inscore ==""):
#         break
#     scores.append(int(inscore))
# scores2 = sorted(scores,reverse = True)
# print(scores2)

# scores = []
# total = inscore = 0
# while(inscore!= -1):
#     inscore = int(input("請輸入%s同學的成績: %s" % (len(scores)+1, "")))
#     if (inscore!= -1):
#         scores.append(inscore)
# print("共有 %d 位學生" % len(scores))
# for score in scores:
#     total += score
# average = total / len(scores)
# scores1= sorted(scores,reverse = True)
# print("成績由大到小%s" % scores1)
# print("總成績:%d 分,平均成績:%5.2f 分" % (total, average))

# tuple1 = (1, 2, 3, 4, 5)
# tuple2 = (1, "香蕉", True)
# tuple3 = ("香蕉", "芭樂", "蘋果", "桃子")
# print(tuple3[1])
# tuple3[1] = "梨子"

# tuple1 = (1, 2, 3, 4, 5)
# list1 = list(tuple1)
# list1.append(😎
# print(list1)

# list2 = [1, 2, 3, 4, 5]
# tuple2 = tuple(list2)
# tuple2.append()
# print(tuple2)

# tuple1 = ("鼠", "牛", "虎", "兔", "龍", "蛇", "馬", "羊", "猴", "雞", "狗", "豬")
# year = int(input("請輸入年份:"))

# zodiac = ('鼠', '牛', '虎', '兔', '龍', '蛇', '馬', '羊', '猴', '雞', '狗', '豬')
# year = int(input('請輸入出生西元年:'))
# zodiac_index = (year - 2020) % 12
# # zodiac_index = (year - 4) % 12
# print("西元%d年的生肖是%s" % (year, zodiac[zodiac_index]))

# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# print(dict1)
# dict2 = dict([["香蕉", 100], ["芭樂", 200], ["蘋果", 300], ["桃子", 400]])
# print(dict2)
# dict3 = dict(香蕉 = 100, 芭樂 = 200, 蘋果 = 300, 桃子 = 400)
# print(dict3)
# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# print(dict1["蘋果"])

# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# print(dict1.get("蘋果"))
# print(dict1.get("梨子"))
# print(dict1.get("蘋果", 80))
# print(dict1.get("鳳梨", 80))

# dict1 = {"春季":"涼爽", "夏季":"熱到爆炸", "秋季":"有點冷", "冬季":"超冷"}
# name = input("請輸入季節名稱:")
# blood = dict1.get(name)
# if blood == None:
#     print("沒有這個季節!")
# else:
#     print(blood)
    
# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# dict1["橘子"] = 500
# print(dict1["橘子"])

# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# dict1["橘子"] = 500
# print(dict1)

# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# del dict1["桃子"]
# print(dict1)
# dict1.clear()
# print(dict1)
# del dict1
# print(dict1)

# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# n = len(dict1)
# print(n)
# dict2 = dict1.copy()
# del dict2["桃子"]
# print(dict2)
# a = "香蕉" in dict1
# print(a)
# b = "APPLE" in dict1
# print(b)
# item1 = dict1.items()
# print(item1)
# key1 = dict1.keys()
# print(key1)
# value1 = dict1.values()
# print(value1)

# dict1 = {"小明": 100, "中明": 100, "大明": 90}
# name = input("請輸入姓名:")
# if name in dict1:
#     print(name + "的成績為:" + str(dict1[name]))
# else:
#     score = input("請輸入成績:")
#     dict1[name] = score
#     print(dict1)

# dict1 = {"小明": 100, "中明": 100, "大明": 90}
# key1 = dict1.keys()
# print(key1)

# for i in key1:
#     print(i , end = ",")

# dict1 = {"香蕉": 100, "芭樂": 200, "蘋果": 300, "桃子": 400}
# key1 = dict1.keys()
# print(key1)
# key2 = list(key1)
# print(key2)
# print(key2[0])

# value1 = dict1.values()
# print(value1)
# value2 = list(value1)
# print(value2)
# print(value2[3])

# dict1 = {"金牌":26, "銀牌": 34, "銅牌": 30}
# listkey = list(dict1.keys())
# listvalue = list(dict1.values())
# for i in range(len(listkey)):
#     print("得到的 %s 數目為 %d 面" % (listkey[i], listvalue[i]))

# dict1 = {"水瓶座": "活潑善變", "雙魚座": "迷人保守", "白羊座": "天生勇敢", "金牛座": "熱情敏感"}
# listkey = list(dict1.keys())
# listvalue = list(dict1.values())
# for i in range(len(listkey)):
#     print("%s 的性格特徵為 %s" % (listkey[i], listvalue[i]))

# dict1 = {"金牌":26, "銀牌": 34, "銅牌": 30}
# item1 = dict1.items()
# for name, num in item1:
#     print("得到的 %s 數目為 %d 面" % (name, num))

# dict1 = {"水瓶座": "活潑善變", "雙魚座": "迷人保守", "白羊座": "天生勇敢", "金牛座": "熱情敏感"}
# item1 = dict1.items()
# for name, num in item1:
#     print("%s 的性格特徵為 %s" % (name, num))

# dict1 = {"蘋果": 100, "香蕉": 200, "芭樂": 300, "桃子": 400}
# # n = dict1.setdefault("蘋果")
# # print(n)
# # n = dict1.setdefault("蘋果", 200)
# # print(n)
# # n = dict1.setdefault("梨子")
# # print(n)
# n = dict1.setdefault("梨子", 200)
# print(dict1)

# dict1 = {"美金":28.02, "日幣":0.2513, "人民幣":4.24}
# input_money = float(input("請輸入台幣:"))
# print("台幣:%5.2f 元" % (input_money), "換成美金:%5.2f 元" % (input_money / dict1["美金"]), "換成日幣:%5.2f 元" % (input_money / dict1["日幣"]), "換成人民幣:%5.2f元" % (input_money / dict1["人民幣"]))

# def say_hello():
#     print("Hello!")
#     print("Hello world!")
#     print("Hello Python!")

# say_hello()

# def get_area(width, height):
#     area = width * height
#     return area

# rect1 = get_area(10, 20)
# print(rect1)

# rect2 = get_area(30, 20)
# print(rect2)

# def circle_area(radius):
#     area = 3.14 * radius * radius
#     lenght = 2 * 3.14 * radius
#     return area, lenght

# area1, lenght1 = circle_area(10)
# print(area1, lenght1)

# def trapezoid_area(top, bottom, height):
#     area = (top + bottom) * height / 2
#     return area

# area1 = trapezoid_area(10, 20, 30)
# print(area)

# def pond(c):
#     f = c * 2.2
#     return f
# input = float(input("請輸入體重公斤數:"))
# print("你的體重為:%5.1f 磅" % pond(input))

# def GetArea(width, height = 12):
#     return width * height

# ret1 = GetArea(10)
# ret2 = GetArea(10, 20)

# def scope():
#     var1 = 1
#     var2 = 2
#     print(var1, var2)

# var1 = 10
# var2 = 20
# scope()
# print(var1, var2)

# def scope():
#     global var1
#     var1 = 1
#     var2 = 2
#     print(var1, var2)

# var1 = 10
# var2 = 20
# scope()
# print(var1, var2)

# def scope():
#     global var1, var2
#     var1 = 1
#     var2 = 2
#     print(var1, var2)

# var1 = 10
# var2 = 20
# scope()
# print(var1, var2)

# def scope():
#     global  var2
#     var1 = 1
#     var2 = 2
#     print(var1, var2)

# var1 = 10
# var2 = 20
# scope()
# print(var1, var2)

# def abs(x):
#     if x >= 0:
#         return x
#     else:
#         return -x
# a = int(input("請輸入數字:"))
# print("絕對值是%d" % abs(a))  
# print("你原本輸入的值是%d" % (a))
# print(chr(70))
# print(chr(36))
# print(pow(2, 3 , 4))
# ret = divmod(10, 3)
# print(ret)
# a = ret[0]
# b = ret[1]
# print(a, b)
# round(50.6789, 2)
# print(round(50.6789, 2))
# print(round(50.6789, 3))
# print(round(50.6789, 4))
# money = int(input("請輸入金額:"))
# spend = int(input("請輸入花費:"))
# ret = divmod(money, spend)  
# print("可維持生活%d天" % (ret[0]))
# print("生活費剩餘%d元" % (ret[1]))
# innum = 0
# list = []
# while innum!= -1:
#     innum = int(input("請輸入電費(-1 end):"))
#     list.append(innum)
#     list.pop()
#     print("共輸入 %d 個數" % len(list))
#     print("最多電費為%d元" % max(list))
#     print("最少電費為%d元" % min(list))
#     print("總電費為%d元" % sum(list))
#     print("電費由小到大排列為:", sorted(list))
# print (max(990 , 1000))
# score =[99, 98, 180, 990, 56, 98 ,22 , 33 ,12 ,21 ,24 ,28 ,27 ,32 ,999]
# print(max(score))
# print(min(score))   
# print(sum(score))
# socre = [99, 98, 180, 990, 56, 98 ,22 , 33 ,12 ,21 ,24 ,28 ,27 ,32 ,999]
# print(sorted(socre))
# print(sorted(socre, reverse=True))
# innum = 0
# list = []
# date = 0
# while date != 4:
#     date += 1
#     innum = int(input("請輸入%d月支出:" % (date)))
#     list.append(innum)
# list.pop()
# print("最多月支出為%d元" % max(list))
# print("最少月支出為%d元" % min(list))
# print("總月支出總和為%d元" % sum(list))
# print("月支出由小到大排列為:", sorted(list))

# list1 = ["This", "is", "a", "book"]

# print(" ".join(list1))

# list3 = ""
# for i in list1:
#     list3 += i + " "
# print(list3)

# str1 = "This is a book"
# str2 = str1.split(" ")
# print(str2)
# str3 = str1.split()
# print(str3)
# str5 = str1.split(",")
# print(str5)

# str1 = "mailto:<EMAIL>"
# print(str1.startswith("mailto:"))
# print(str1.startswith("to:"))

# print(str1.endswith(".tw"))
# print(str1.endswith(".com"))

# web = input("請輸入圖片檔案名稱:")
# if web.endswith(".JPG") or web.endswith(".jpg"):
#     print("圖片格式是JPG")
# else:
#     print("圖片格式不是JPG")

# str1 = "python"
# print(str1.ljust(12))
# print(str1.ljust(12 , "$"))
# print(str1.ljust(4 , "$"))

# str1 = "python"
# print(str1.rjust(12))
# print(str1.rjust(12 , "$"))
# print(str1.rjust(4 , "$"))

# str1 = "    I love python.    "
# print(str1.lstrip())
# print(str1.rstrip())
# print(str1.strip())

# listname = ["鐘明達", "鄭廣月", "何美麗"]
# seasonone = [34210, 23600, 145000]
# seasontwo = [9000, 23900, 83400]
# seasonthree = [186500, 127800, 100000]
# seasonfour = [78900, 125000, 90000]
# print("姓名     第一季 第二季  第三季  第四季")
# for i in range(3):
#     print(listname[i].ljust(5), str(seasonone[i]).rjust(6), str(seasontwo[i]).rjust(6), str(seasonthree[i]).rjust(7), str(seasonfour[i]).rjust(7))

# str1 = "I love python"
# print(str1.find("o"))
# print(str1.find("python"))
# print(str1.find(" x "))

# str1 = "I love python"
# print(str1.replace("o" , "&"))
# print(str1.replace("o" , "&" , 1))

# str1 = "I love python"
# print(str1.replace("o" , "0", ))

# str1 = "I love python"
# print(str1[:4] + str1[4:].replace("o" , "0"))

# date1 = "2017-8-23"
# date1 = " 西元 " + date1
# date1 = date1.replace("-", " 年 ", 1)
# date1 = date1.replace("-", " 月 ", 1)
# date1 += " 日 "
# print(date1)

# date1 = "10:23:41"
# date1 = date1.replace(":", " 點 ", 1)
# date1 = date1.replace(":", " 分 ", 1)
# date1 += " 秒 "
# print(date1)

# import random

# for i in range(5):
#     print(random.randint(1, 10), end=",")

# num = random.randint(1, 10)

# from random import randint
# num = randint(1, 10)

# from random import randint as ri
# num = ri(1, 10)

# import random

# for i in range(5):
#     # print(random.randint(1, 10), end=",")
#     print(random.randrange(1, 10, 2), end=",")

# import random

# while True:
#     inkey = input("案任意件在按[Enter]鍵擲骰子,直接按[Enter]鍵結束程式:")
#     if len(inkey) > 0:
#        num = random.randint(1, 6)
#        print("骰子數字為:" , num)
#     else:
#         print("遊戲結束!")
#         break

# import random
# for i in range(5):
#     print(random.choice("abcdefg"), end="")

# import random
# for i in range(5):
#     print(random.choice("1, 2, 3, 4, 5, 6, 7"), end="")

# import random
# for i in range(5):
#     print(random.choice(["剪刀", "石頭", "布"]))

# import random
# print(random.sample("123456789", 3))
# print(random.sample([1, 2, 3, 4, 5, 6, 7, 8, 9], 3))
# print(random.sample([1, 2, 3, 4, 5, 6, 7], 8))

# import random

# list1 = random.sample(range(1, 50), 7)
# special = list1.pop()
# list1.sort()
# print("本期大樂透號碼為:", end="")
# for i in range(6):
#     if i == 5: 
#         print(str(list1[i]))
#     else: 
#         print(str(list1[i]), end=",")

# print("本期大樂透特別號碼為:", special)

# import random

# list1 = random.sample(range(1, 50), 5)
# special = list1.pop()
# list1.sort()
# print("本期四星彩號碼為:", end="")

# for i in range(4):
#     if i == 5: 
#         print(str(list1[i]))
#     else: 
#         print(str(list1[i]), 
# import time
# print(time.time())

# import time
# time1 = time.time()

# for i in range(100000000):
#     pass

# time2 = time.time()

# print("執行時間為:", time2 - time1)

# import time

# print(time.localtime())

# import time

# time = time.localtime(time.time())
# print(time.tm_year)
# print(time[0])

# import time as t

# print(t.ctime())
# print(t.ctime(t.time()))

# import time
# time1 = time.localtime().tm_