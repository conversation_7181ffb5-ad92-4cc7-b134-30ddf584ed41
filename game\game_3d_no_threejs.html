<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧟 末日生存射擊遊戲 - 3D版 (無Three.js)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
            overflow: hidden;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #228B22 100%);
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 4px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 15%; }
        .scope-mark.bottom { bottom: 15%; }
        .scope-mark.left { left: 15%; }
        .scope-mark.right { right: 15%; }

        /* 瞄準模式下的效果 */
        body.scoped #crosshair {
            opacity: 0.3;
        }

        body.scoped {
            background: #000;
        }

        /* HUD */
        #hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 50;
        }

        #stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            pointer-events: auto;
        }

        #gameStatus {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            text-align: right;
        }

        /* 開始菜單 */
        #startMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 300;
        }

        #startMenu h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .menu-info {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            line-height: 1.6;
        }

        #startButton {
            padding: 15px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            pointer-events: auto;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        #startButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .hidden {
            display: none !important;
        }

        /* 瞄準狀態指示 */
        #scopeIndicator {
            position: absolute;
            top: 60px;
            right: 20px;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            z-index: 300;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- 遊戲畫布 -->
        <canvas id="gameCanvas"></canvas>
        
        <!-- HUD -->
        <div id="hud">
            <div id="crosshair"></div>
            
            <!-- 瞄準鏡 -->
            <div id="scope">
                <div class="scope-marks">
                    <div class="scope-mark horizontal top"></div>
                    <div class="scope-mark horizontal bottom"></div>
                    <div class="scope-mark vertical left"></div>
                    <div class="scope-mark vertical right"></div>
                </div>
            </div>
            
            <!-- 瞄準狀態指示 -->
            <div id="scopeIndicator">🔍 瞄準: <span id="scopeStatus">關閉</span></div>
            
            <div id="gameStatus">
                <div>分數: <span id="scoreValue">0</span></div>
                <div>🔫 <span id="weaponInfo">手槍</span></div>
                <div>敵人: <span id="enemyCount">0</span></div>
            </div>
            
            <div id="stats">
                <div style="color: #ff4444;">❤️ 生命值: <span id="healthValue">100</span></div>
                <div style="color: #44ff44;">🔫 彈藥: <span id="ammoValue">30/30</span></div>
                <div style="color: #44ffff;">🔧 廢料: <span id="scrapValue">0</span> | 💊 醫療包: <span id="medkitValue">0</span></div>
            </div>
        </div>
        
        <!-- 開始菜單 -->
        <div id="startMenu">
            <h1>🧟 末日生存射擊</h1>
            <div class="menu-info">
                <p><strong>🎮 遊戲說明：</strong></p>
                <p>• WASD 移動，空格鍵跳躍，鼠標控制視角</p>
                <p>• <strong style="color: #ff6b6b;">左鍵射擊</strong>，<strong style="color: #4ecdc4;">右鍵按住開啟瞄準鏡</strong></p>
                <p>• R 重新裝彈，Q 切換武器</p>
                <p>• 消滅喪屍獲得分數，解鎖更強武器</p>
                <br>
                <p><strong>🔍 瞄準鏡功能：</strong></p>
                <p style="color: #4ecdc4;">• 右鍵按住開啟瞄準鏡</p>
                <p>• 提高射擊精度和傷害</p>
                <p>• 降低鼠標靈敏度便於精確瞄準</p>
                <br>
                <p><strong>🎯 3D版本特色：</strong></p>
                <p>• 原生Canvas 3D渲染</p>
                <p>• 不依賴外部庫</p>
                <p>• 完整瞄準鏡系統</p>
            </div>
            <button id="startButton">🚀 開始遊戲</button>
        </div>
    </div>

    <script>
        // 3D遊戲引擎（不依賴Three.js）
        class Simple3DGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.isPointerLocked = false;
                this.gameState = 'menu';
                this.isScoped = false;
                
                // 遊戲數據
                this.player = {
                    x: 0, y: 0, z: 0,
                    angle: 0, pitch: 0,
                    health: 100,
                    score: 0
                };
                
                this.weapons = [
                    { name: '手槍', icon: '🔫', damage: 25, ammo: 30, maxAmmo: 30, unlockScore: 0 },
                    { name: '霰彈槍', icon: '💥', damage: 50, ammo: 8, maxAmmo: 8, unlockScore: 500 },
                    { name: '突擊步槍', icon: '🔥', damage: 35, ammo: 30, maxAmmo: 30, unlockScore: 1500 },
                    { name: '狙擊槍', icon: '⚡', damage: 100, ammo: 5, maxAmmo: 5, unlockScore: 3000 }
                ];
                
                this.currentWeaponIndex = 0;
                this.enemies = [];
                this.bullets = [];
                
                this.init();
            }
            
            init() {
                this.setupCanvas();
                this.setupEventListeners();
                this.gameLoop();
                console.log('🎮 3D遊戲引擎已初始化（無Three.js依賴）');
            }
            
            setupCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
            }
            
            setupEventListeners() {
                // 開始按鈕
                document.getElementById('startButton').addEventListener('click', () => {
                    this.startGame();
                });
                
                // 鼠標事件
                document.addEventListener('mousedown', (e) => {
                    if (this.gameState === 'playing' && this.isPointerLocked) {
                        if (e.button === 0) { // 左鍵
                            this.shoot();
                        } else if (e.button === 2) { // 右鍵
                            this.toggleScope(true);
                        }
                        e.preventDefault();
                    }
                });
                
                document.addEventListener('mouseup', (e) => {
                    if (this.gameState === 'playing' && this.isPointerLocked) {
                        if (e.button === 2) { // 右鍵
                            this.toggleScope(false);
                        }
                    }
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (this.isPointerLocked) {
                        const sensitivity = this.isScoped ? 0.0008 : 0.002;
                        this.player.angle -= e.movementX * sensitivity;
                        this.player.pitch -= e.movementY * sensitivity;
                        this.player.pitch = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.player.pitch));
                    }
                });
                
                // 禁用右鍵菜單
                document.addEventListener('contextmenu', (e) => e.preventDefault());
                
                // 鍵盤事件
                document.addEventListener('keydown', (e) => {
                    if (this.gameState === 'playing') {
                        switch(e.code) {
                            case 'KeyR':
                                this.reload();
                                break;
                            case 'KeyQ':
                                this.switchWeapon();
                                break;
                            case 'Digit1':
                                this.selectWeapon(0);
                                break;
                            case 'Digit2':
                                this.selectWeapon(1);
                                break;
                            case 'Digit3':
                                this.selectWeapon(2);
                                break;
                            case 'Digit4':
                                this.selectWeapon(3);
                                break;
                        }
                    }
                });
                
                // 指針鎖定事件
                document.addEventListener('pointerlockchange', () => {
                    this.isPointerLocked = document.pointerLockElement === document.body;
                });
                
                // 窗口大小變化
                window.addEventListener('resize', () => {
                    this.setupCanvas();
                });
            }
            
            startGame() {
                document.getElementById('startMenu').classList.add('hidden');
                document.body.requestPointerLock();
                this.gameState = 'playing';
                this.spawnEnemies();
                console.log('🎮 遊戲開始');
            }
            
            toggleScope(enable) {
                this.isScoped = enable;
                const scope = document.getElementById('scope');
                const body = document.body;
                const statusElement = document.getElementById('scopeStatus');
                
                if (enable) {
                    scope.classList.add('active');
                    body.classList.add('scoped');
                    if (statusElement) {
                        statusElement.textContent = '開啟';
                        statusElement.style.color = '#00ff00';
                    }
                    console.log('🔍 瞄準鏡開啟');
                } else {
                    scope.classList.remove('active');
                    body.classList.remove('scoped');
                    if (statusElement) {
                        statusElement.textContent = '關閉';
                        statusElement.style.color = '#ffffff';
                    }
                    console.log('🔍 瞄準鏡關閉');
                }
            }
            
            shoot() {
                const weapon = this.weapons[this.currentWeaponIndex];
                if (weapon.ammo <= 0) {
                    this.reload();
                    return;
                }
                
                weapon.ammo--;
                
                // 瞄準時精度更高
                const accuracy = this.isScoped ? 0.98 : 0.85;
                const spread = this.isScoped ? 0.02 : 0.1;
                const damage = this.isScoped ? weapon.damage * 1.3 : weapon.damage;
                
                // 添加子彈
                const angleSpread = (Math.random() - 0.5) * spread;
                const finalAngle = this.player.angle + angleSpread;
                
                this.bullets.push({
                    x: this.player.x,
                    y: this.player.y,
                    z: this.player.z,
                    vx: Math.cos(finalAngle) * 500,
                    vz: Math.sin(finalAngle) * 500,
                    damage: damage,
                    life: 3
                });
                
                const scopeText = this.isScoped ? ' (瞄準)' : '';
                console.log(`🔫 ${weapon.icon} 射擊${scopeText}！剩餘彈藥: ${weapon.ammo}`);
                this.updateUI();
            }
            
            reload() {
                const weapon = this.weapons[this.currentWeaponIndex];
                weapon.ammo = weapon.maxAmmo;
                console.log(`🔄 ${weapon.icon} 重新裝彈完成`);
                this.updateUI();
            }
            
            switchWeapon() {
                let nextIndex = (this.currentWeaponIndex + 1) % this.weapons.length;
                while (nextIndex !== this.currentWeaponIndex && this.player.score < this.weapons[nextIndex].unlockScore) {
                    nextIndex = (nextIndex + 1) % this.weapons.length;
                }
                this.currentWeaponIndex = nextIndex;
                this.updateUI();
            }
            
            selectWeapon(index) {
                if (index < this.weapons.length && this.player.score >= this.weapons[index].unlockScore) {
                    this.currentWeaponIndex = index;
                    this.updateUI();
                }
            }
            
            spawnEnemies() {
                // 生成敵人
                for (let i = 0; i < 5; i++) {
                    this.enemies.push({
                        x: (Math.random() - 0.5) * 1000,
                        y: 0,
                        z: (Math.random() - 0.5) * 1000,
                        health: 50,
                        speed: 20
                    });
                }
            }
            
            updateUI() {
                const weapon = this.weapons[this.currentWeaponIndex];
                document.getElementById('scoreValue').textContent = this.player.score;
                document.getElementById('weaponInfo').textContent = `${weapon.icon} ${weapon.name}`;
                document.getElementById('healthValue').textContent = this.player.health;
                document.getElementById('ammoValue').textContent = `${weapon.ammo}/${weapon.maxAmmo}`;
                document.getElementById('enemyCount').textContent = this.enemies.length;
            }
            
            render() {
                const ctx = this.ctx;
                const width = this.canvas.width;
                const height = this.canvas.height;
                
                // 清除畫布
                ctx.fillStyle = '#87CEEB';
                ctx.fillRect(0, 0, width, height);
                
                // 繪製地面
                ctx.fillStyle = '#228B22';
                ctx.fillRect(0, height * 0.7, width, height * 0.3);
                
                if (this.gameState === 'playing') {
                    // 繪製3D場景
                    this.render3DScene();
                }
            }
            
            render3DScene() {
                const ctx = this.ctx;
                const width = this.canvas.width;
                const height = this.canvas.height;
                
                // 繪製敵人（簡化的3D投影）
                this.enemies.forEach(enemy => {
                    const dx = enemy.x - this.player.x;
                    const dz = enemy.z - this.player.z;
                    const distance = Math.sqrt(dx*dx + dz*dz);
                    
                    if (distance > 0) {
                        // 簡單的3D投影
                        const angle = Math.atan2(dz, dx) - this.player.angle;
                        const screenX = width/2 + Math.sin(angle) * 200;
                        const screenY = height/2 - this.player.pitch * 100;
                        const size = Math.max(10, 100 / (distance / 100));
                        
                        if (screenX > -size && screenX < width + size) {
                            ctx.fillStyle = '#ff0000';
                            ctx.fillRect(screenX - size/2, screenY - size/2, size, size);
                            
                            // 敵人標記
                            ctx.fillStyle = 'white';
                            ctx.font = `${size/2}px Arial`;
                            ctx.textAlign = 'center';
                            ctx.fillText('🧟', screenX, screenY + size/4);
                        }
                    }
                });
                
                // 繪製子彈
                this.bullets.forEach(bullet => {
                    const dx = bullet.x - this.player.x;
                    const dz = bullet.z - this.player.z;
                    const distance = Math.sqrt(dx*dx + dz*dz);
                    
                    if (distance > 0) {
                        const angle = Math.atan2(dz, dx) - this.player.angle;
                        const screenX = width/2 + Math.sin(angle) * 200;
                        const screenY = height/2;
                        
                        if (screenX > 0 && screenX < width) {
                            ctx.fillStyle = this.isScoped ? '#ffff00' : '#ff0000';
                            ctx.fillRect(screenX - 2, screenY - 2, 4, 4);
                        }
                    }
                });
                
                // 繪製UI信息
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('3D渲染模式 (原生Canvas)', width/2, 50);
                ctx.font = '12px Arial';
                ctx.fillText('完整瞄準鏡功能已啟用', width/2, 70);
            }
            
            update(deltaTime) {
                if (this.gameState !== 'playing') return;
                
                // 更新子彈
                this.bullets = this.bullets.filter(bullet => {
                    bullet.x += bullet.vx * deltaTime;
                    bullet.z += bullet.vz * deltaTime;
                    bullet.life -= deltaTime;
                    
                    // 檢查碰撞
                    for (let i = this.enemies.length - 1; i >= 0; i--) {
                        const enemy = this.enemies[i];
                        const dx = bullet.x - enemy.x;
                        const dz = bullet.z - enemy.z;
                        const distance = Math.sqrt(dx*dx + dz*dz);
                        
                        if (distance < 50) {
                            enemy.health -= bullet.damage;
                            if (enemy.health <= 0) {
                                this.player.score += 100;
                                this.enemies.splice(i, 1);
                                console.log('🎯 擊殺敵人！分數: ' + this.player.score);
                            }
                            return false; // 移除子彈
                        }
                    }
                    
                    return bullet.life > 0;
                });
                
                // 更新敵人
                this.enemies.forEach(enemy => {
                    const dx = this.player.x - enemy.x;
                    const dz = this.player.z - enemy.z;
                    const distance = Math.sqrt(dx*dx + dz*dz);
                    
                    if (distance > 0) {
                        enemy.x += (dx / distance) * enemy.speed * deltaTime;
                        enemy.z += (dz / distance) * enemy.speed * deltaTime;
                    }
                });
                
                // 生成新敵人
                if (this.enemies.length < 3) {
                    this.spawnEnemies();
                }
                
                this.updateUI();
            }
            
            gameLoop() {
                let lastTime = 0;
                
                const loop = (currentTime) => {
                    const deltaTime = (currentTime - lastTime) / 1000;
                    lastTime = currentTime;
                    
                    this.update(deltaTime);
                    this.render();
                    
                    requestAnimationFrame(loop);
                };
                
                requestAnimationFrame(loop);
            }
        }
        
        // 初始化遊戲
        window.addEventListener('load', () => {
            console.log('🎮 載入3D遊戲（無Three.js依賴）...');
            const game = new Simple3DGame();
            window.game = game; // 全局訪問
        });
    </script>
</body>
</html>
