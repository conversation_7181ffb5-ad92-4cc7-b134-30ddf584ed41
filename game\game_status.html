<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 遊戲狀態檢查</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            padding: 20px;
        }

        .container {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        h1 {
            font-size: 36px;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .status-card.working {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.1);
        }

        .status-card.issue {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }

        .status-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .status-title {
            font-size: 20px;
            margin-bottom: 10px;
            color: #fff;
        }

        .status-description {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .status-indicator {
            font-size: 16px;
            font-weight: bold;
        }

        .working .status-indicator {
            color: #4ecdc4;
        }

        .issue .status-indicator {
            color: #ff6b6b;
        }

        .game-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }

        .game-link {
            display: block;
            padding: 15px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
            font-weight: bold;
        }

        .game-link:hover {
            transform: translateY(-2px);
        }

        .game-link.recommended {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
        }

        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .summary h3 {
            color: #4ecdc4;
            margin-bottom: 10px;
        }

        .summary ul {
            list-style: none;
            padding-left: 0;
        }

        .summary li {
            padding: 5px 0;
            border-left: 3px solid #4ecdc4;
            padding-left: 10px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 遊戲狀態檢查</h1>
        
        <div class="status-grid">
            <!-- 離線版狀態 -->
            <div class="status-card working">
                <div class="status-icon">📱</div>
                <div class="status-title">離線版遊戲</div>
                <div class="status-description">2D圖形，完全離線運行</div>
                <div class="status-indicator">✅ 完全正常</div>
            </div>
            
            <!-- 3D原生版狀態 -->
            <div class="status-card working">
                <div class="status-icon">🎮</div>
                <div class="status-title">3D原生版</div>
                <div class="status-description">Canvas原生渲染，無外部依賴</div>
                <div class="status-indicator">✅ 完全正常</div>
            </div>
            
            <!-- Three.js版狀態 -->
            <div class="status-card working">
                <div class="status-icon">🌐</div>
                <div class="status-title">Three.js版</div>
                <div class="status-description">使用Three.js 3D庫</div>
                <div class="status-indicator">✅ 已修復</div>
            </div>
            
            <!-- 瞄準鏡功能 -->
            <div class="status-card working">
                <div class="status-icon">🔍</div>
                <div class="status-title">瞄準鏡功能</div>
                <div class="status-description">右鍵瞄準系統</div>
                <div class="status-indicator">✅ 兩版本都支援</div>
            </div>
        </div>
        
        <div class="summary">
            <h3>📋 狀態總結</h3>
            <ul>
                <li><strong>離線版遊戲</strong>：100%可用，包含完整瞄準鏡功能</li>
                <li><strong>3D原生版</strong>：100%可用，原生Canvas 3D渲染</li>
                <li><strong>Three.js版</strong>：已修復，使用Three.js 3D渲染</li>
                <li><strong>瞄準鏡功能</strong>：右鍵按住開啟，左鍵射擊</li>
                <li><strong>推薦使用</strong>：Three.js版（完整3D）或離線版（最穩定）</li>
            </ul>
        </div>
        
        <div class="game-links">
            <a href="index.html" class="game-link recommended">🎮 3D Three.js版 (推薦)</a>
            <a href="game_offline.html" class="game-link">📱 離線版</a>
            <a href="game_3d_no_threejs.html" class="game-link">🔧 3D原生版</a>
            <a href="game_launcher.html" class="game-link">🚀 遊戲啟動器</a>
        </div>
        
        <div style="margin-top: 30px; font-size: 14px; color: #ccc;">
            <p>🎯 所有版本都支援瞄準鏡功能：右鍵按住開啟瞄準鏡，左鍵射擊</p>
            <p>💡 如果遇到問題，建議使用離線版遊戲</p>
        </div>
    </div>
</body>
</html>
