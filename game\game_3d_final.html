<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧟 末日生存射擊遊戲 - 3D版 (瞄準鏡修復)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
            overflow: hidden;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        /* 載入畫面 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            z-index: 150;
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 4px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 15%; }
        .scope-mark.bottom { bottom: 15%; }
        .scope-mark.left { left: 15%; }
        .scope-mark.right { right: 15%; }

        /* 瞄準模式下的效果 */
        body.scoped #crosshair {
            opacity: 0.3;
        }

        body.scoped {
            background: #000;
        }

        /* HUD */
        #hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 50;
        }

        #stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            pointer-events: auto;
        }

        #gameStatus {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            text-align: right;
        }

        /* 調試信息 */
        #debugInfo {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 300;
            max-width: 300px;
        }

        /* 開始菜單 */
        #startMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #startMenu h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .menu-info {
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            line-height: 1.6;
        }

        #startButton {
            padding: 15px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            pointer-events: auto;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        #startButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- 載入畫面 -->
        <div id="loading">
            <div>🎮 載入3D遊戲中...</div>
            <div style="margin-top: 20px; font-size: 16px;">正在初始化瞄準鏡系統...</div>
        </div>
        
        <!-- 調試信息 -->
        <div id="debugInfo">
            <h4>🔍 瞄準鏡調試</h4>
            <div>右鍵狀態: <span id="rightClickStatus">未按下</span></div>
            <div>瞄準鏡: <span id="scopeStatus">關閉</span></div>
            <div>鼠標鎖定: <span id="lockStatus">未鎖定</span></div>
            <div>遊戲狀態: <span id="gameStateStatus">菜單</span></div>
        </div>
        
        <!-- HUD -->
        <div id="hud">
            <div id="crosshair"></div>
            
            <!-- 瞄準鏡 -->
            <div id="scope">
                <div class="scope-marks">
                    <div class="scope-mark horizontal top"></div>
                    <div class="scope-mark horizontal bottom"></div>
                    <div class="scope-mark vertical left"></div>
                    <div class="scope-mark vertical right"></div>
                </div>
            </div>
            
            <div id="gameStatus">
                <div>分數: <span id="scoreValue">0</span></div>
                <div>🔫 <span id="weaponInfo">手槍</span></div>
                <div>敵人: <span id="enemyCount">0</span></div>
            </div>
            
            <div id="stats">
                <div style="color: #ff4444;">❤️ 生命值: <span id="healthValue">100</span></div>
                <div style="color: #44ff44;">🔫 彈藥: <span id="ammoValue">30/30</span></div>
                <div style="color: #44ffff;">🔧 廢料: <span id="scrapValue">0</span> | 💊 醫療包: <span id="medkitValue">0</span></div>
            </div>
        </div>
        
        <!-- 開始菜單 -->
        <div id="startMenu">
            <h1>🧟 末日生存射擊</h1>
            <div class="menu-info">
                <p><strong>🎮 遊戲說明：</strong></p>
                <p>• WASD 移動，空格鍵跳躍，鼠標控制視角</p>
                <p>• <strong style="color: #ff6b6b;">左鍵射擊</strong>，<strong style="color: #4ecdc4;">右鍵按住開啟瞄準鏡</strong></p>
                <p>• R 重新裝彈，Q 切換武器</p>
                <p>• 消滅喪屍獲得分數，解鎖更強武器</p>
                <br>
                <p><strong>🔍 瞄準鏡功能：</strong></p>
                <p style="color: #4ecdc4;">• 右鍵按住開啟瞄準鏡</p>
                <p>• 提高射擊精度和傷害</p>
                <p>• 降低鼠標靈敏度便於精確瞄準</p>
            </div>
            <button id="startButton">🚀 開始遊戲</button>
        </div>
    </div>

    <!-- Three.js 庫 -->
    <script src="js/three.min.js"></script>
    
    <!-- 遊戲主程式 -->
    <script src="js/game.js"></script>
    
    <!-- 瞄準鏡修復腳本 -->
    <script>
        // 瞄準鏡狀態管理
        let scopeManager = {
            isScoped: false,
            gameInstance: null,
            
            init() {
                console.log('🔍 初始化瞄準鏡管理器...');
                this.setupEventListeners();
                this.waitForGame();
            },
            
            waitForGame() {
                if (window.game) {
                    this.gameInstance = window.game;
                    console.log('✅ 遊戲實例已找到');
                    this.enhanceGame();
                } else {
                    console.log('⏳ 等待遊戲實例...');
                    setTimeout(() => this.waitForGame(), 1000);
                }
            },
            
            enhanceGame() {
                // 覆蓋原始的鼠標事件處理
                const originalSetupEventListeners = this.gameInstance.setupEventListeners;
                this.gameInstance.setupEventListeners = function() {
                    // 調用原始方法但跳過鼠標事件
                    console.log('🔧 設置增強的事件監聽器...');
                    
                    // 只設置鍵盤和其他事件，跳過鼠標事件
                    document.addEventListener('keydown', (event) => this.onKeyDown(event));
                    document.addEventListener('keyup', (event) => this.onKeyUp(event));
                    document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
                    window.addEventListener('resize', () => this.onWindowResize());
                    
                    // 使用我們自定義的鼠標事件處理
                    scopeManager.setupMouseEvents();
                };
            },
            
            setupEventListeners() {
                // 自定義鼠標事件處理
                document.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                document.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                document.addEventListener('contextmenu', (e) => e.preventDefault());
            },
            
            setupMouseEvents() {
                console.log('🖱️ 設置自定義鼠標事件...');
                // 這個方法會被遊戲調用，但我們已經在init中設置了
            },
            
            handleMouseDown(event) {
                this.updateDebugInfo('rightClickStatus', event.button === 2 ? '按下' : '未按下');
                
                if (this.gameInstance && this.gameInstance.gameState === 'playing' && this.gameInstance.isPointerLocked) {
                    if (event.button === 2) { // 右鍵
                        this.toggleScope(true);
                        console.log('🔍 右鍵按下 - 開啟瞄準鏡');
                    } else if (event.button === 0) { // 左鍵
                        this.gameInstance.shoot();
                        console.log('🔫 左鍵按下 - 射擊');
                    }
                    event.preventDefault();
                }
            },
            
            handleMouseUp(event) {
                this.updateDebugInfo('rightClickStatus', '未按下');
                
                if (this.gameInstance && this.gameInstance.gameState === 'playing' && this.gameInstance.isPointerLocked) {
                    if (event.button === 2) { // 右鍵
                        this.toggleScope(false);
                        console.log('🔍 右鍵釋放 - 關閉瞄準鏡');
                    }
                    event.preventDefault();
                }
            },
            
            handleMouseMove(event) {
                if (this.gameInstance && this.gameInstance.onMouseMove) {
                    this.gameInstance.onMouseMove(event);
                }
            },
            
            toggleScope(enable) {
                this.isScoped = enable;
                const scope = document.getElementById('scope');
                const body = document.body;
                
                if (enable) {
                    scope.classList.add('active');
                    body.classList.add('scoped');
                    this.updateDebugInfo('scopeStatus', '開啟');
                    
                    // 設置瞄準鏡靈敏度
                    if (this.gameInstance) {
                        this.gameInstance.scopedSensitivity = 0.0008;
                        this.gameInstance.controls.isScoped = true;
                    }
                } else {
                    scope.classList.remove('active');
                    body.classList.remove('scoped');
                    this.updateDebugInfo('scopeStatus', '關閉');
                    
                    // 恢復正常靈敏度
                    if (this.gameInstance) {
                        this.gameInstance.scopedSensitivity = null;
                        this.gameInstance.controls.isScoped = false;
                    }
                }
            },
            
            updateDebugInfo(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }
        };
        
        // 監控遊戲狀態
        function monitorGameState() {
            if (window.game) {
                scopeManager.updateDebugInfo('gameStateStatus', window.game.gameState);
                scopeManager.updateDebugInfo('lockStatus', window.game.isPointerLocked ? '已鎖定' : '未鎖定');
            }
            setTimeout(monitorGameState, 1000);
        }
        
        // 初始化
        window.addEventListener('load', function() {
            console.log('🎮 載入瞄準鏡修復版本...');
            scopeManager.init();
            monitorGameState();
        });
    </script>
</body>
</html>
