# 🧟 末日生存射擊遊戲

一個基於Three.js的3D第一人稱射擊遊戲，在後末日城市中對抗喪屍群！

## 🎮 遊戲特色

- **3D第一人稱射擊**：流暢的FPS體驗
- **多種武器系統**：🔫 手槍 → 💥 霰彈槍 → 🔥 突擊步槍 → ⚡ 狙擊槍
- **智能AI敵人**：喪屍會在一定距離內發現並追逐玩家
- **城市環境**：宏偉的後末日都市場景
- **資源收集**：彈藥、醫療包、廢料
- **武器升級**：通過分數解鎖更強武器

## 🚀 快速開始

### 方法一：一鍵啟動（推薦）

#### Windows用戶：
1. 雙擊 `start_game.bat`
2. 等待瀏覽器自動打開
3. 開始遊戲！

#### Mac/Linux用戶：
1. 在終端中運行：`./start_game.sh`
2. 或者：`bash start_game.sh`
3. 等待瀏覽器自動打開
4. 開始遊戲！

### 方法二：手動啟動

1. 確保已安裝Python 3.x
2. 在遊戲目錄中運行：
   ```bash
   python start_server.py
   ```
3. 在瀏覽器中打開：`http://localhost:8000`

### 方法三：直接打開（可能有限制）

直接雙擊 `index.html` 文件，但可能會遇到CORS限制。

## 🎯 遊戲操作

### 基本控制
- **WASD** - 移動
- **鼠標** - 控制視角
- **左鍵** - 射擊
- **空格鍵** - 跳躍
- **R** - 重新裝彈

### 武器切換
- **Q** - 循環切換武器
- **1-4** - 快速切換武器
- **Tab** - 打開武器商店
- **點擊武器槽** - 直接切換

### 特殊功能
- **T** - 生成測試目標（調試用）

## 🔫 武器系統

| 武器 | 圖標 | 解鎖分數 | 傷害 | 特點 |
|------|------|----------|------|------|
| 手槍 | 🔫 | 0分 | 35 | 可靠的起始武器 |
| 霰彈槍 | 💥 | 500分 | 80 | 近距離高傷害 |
| 突擊步槍 | 🔥 | 1500分 | 45 | 平衡的全能武器 |
| 狙擊槍 | ⚡ | 3000分 | 150 | 遠程一擊必殺 |

## 🧟 敵人類型

- **普通喪屍** - 基礎敵人，100分
- **快速喪屍** - 移動迅速，150分
- **坦克喪屍** - 血厚攻高，200分

## 📋 系統需求

- **瀏覽器**：Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
- **Python**：3.6+ （用於本地服務器）
- **網絡**：需要連接互聯網載入Three.js庫

## 🛠️ 技術特點

- **Three.js r150** - 3D圖形渲染
- **WebGL** - 硬件加速
- **響應式設計** - 適應不同屏幕尺寸
- **性能優化** - 60FPS流暢體驗
- **錯誤處理** - 完善的錯誤恢復機制

## 🎨 遊戲界面

- **十字準心** - 精確瞄準
- **血量條** - 視覺化生命值
- **武器選擇器** - 快速武器切換
- **彈藥顯示** - 實時彈藥狀態
- **傷害數字** - 命中反饋
- **敵人血量條** - 敵人狀態顯示

## 🔧 故障排除

### 遊戲無法載入
1. 檢查網絡連接
2. 確保Python已正確安裝
3. 嘗試使用不同端口：`python start_server.py --port 8001`

### 性能問題
1. 關閉其他瀏覽器標籤
2. 更新顯卡驅動
3. 使用Chrome瀏覽器以獲得最佳性能

### 控制問題
1. 確保已點擊遊戲畫面獲得焦點
2. 檢查瀏覽器是否支援指針鎖定API

## 📞 支援

如果遇到問題，請檢查瀏覽器控制台的錯誤信息。

## 🎉 開始遊戲

準備好在末日世界中生存了嗎？運行啟動腳本，拿起武器，對抗喪屍群！

**祝你好運，生存者！** 🧟‍♂️💀🔫
