<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D 第一人稱射擊遊戲</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #000;
            overflow: hidden;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* HUD 元素 */
        #hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        /* 準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
        }

        #crosshair::before {
            top: 50%;
            left: 8px;
            width: 4px;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 8px;
            width: 2px;
            height: 4px;
            transform: translateX(-50%);
        }

        /* 生命值和彈藥顯示 */
        #stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        #health {
            color: #ff4444;
            margin-bottom: 10px;
        }

        #ammo {
            color: #44ff44;
            margin-bottom: 10px;
        }

        #weapon {
            color: #ffff44;
            margin-bottom: 10px;
        }

        #resources {
            color: #44ffff;
            font-size: 14px;
        }

        #nearbyItems {
            color: #ffff44;
            font-size: 14px;
            margin-top: 10px;
        }

        /* 遊戲狀態顯示 */
        #gameStatus {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            text-align: right;
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 血量條 */
        #healthBar {
            position: absolute;
            bottom: 80px;
            left: 20px;
            width: 200px;
            height: 20px;
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid #fff;
            border-radius: 10px;
        }

        #healthFill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ffff00, #00ff00);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        /* 彈藥顯示 */
        #ammoDisplay {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 4px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 15%; }
        .scope-mark.bottom { bottom: 15%; }
        .scope-mark.left { left: 15%; }
        .scope-mark.right { right: 15%; }

        /* 瞄準模式下的效果 */
        body.scoped #crosshair {
            opacity: 0.3;
        }

        body.scoped {
            background: #000;
        }

        /* 武器圖標 */
        .weapon-icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-right: 8px;
            vertical-align: middle;
            font-size: 20px;
            text-align: center;
            line-height: 24px;
        }

        /* 武器選擇器 */
        #weaponSelector {
            position: absolute;
            bottom: 80px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .weapon-slot {
            display: flex;
            align-items: center;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #666;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
            min-width: 120px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .weapon-slot:hover:not(.locked) {
            border-color: #aaa;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }

        .weapon-slot.active {
            border-color: #ffff44;
            background: rgba(255, 255, 68, 0.2);
            box-shadow: 0 0 10px rgba(255, 255, 68, 0.5);
        }

        .weapon-slot.locked {
            opacity: 0.5;
            border-color: #444;
        }

        .weapon-slot .weapon-icon {
            font-size: 18px;
            margin-right: 8px;
        }

        .weapon-slot .weapon-name {
            flex: 1;
            font-weight: bold;
        }

        .weapon-slot .weapon-key {
            font-size: 12px;
            color: #aaa;
            margin-left: 8px;
        }

        /* 分數顯示 */
        #score {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* 開始菜單 */
        #startMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #startMenu h1 {
            font-size: 48px;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        #startButton {
            padding: 15px 30px;
            font-size: 24px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s;
            pointer-events: auto;
        }

        #startButton:hover {
            background: #ff6666;
        }

        /* 遊戲結束畫面 */
        #gameOver {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #gameOver h2 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #ff4444;
        }

        #finalScore {
            font-size: 24px;
            margin-bottom: 30px;
        }

        #restartButton {
            padding: 15px 30px;
            font-size: 20px;
            background: #44ff44;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s;
            pointer-events: auto;
        }

        #restartButton:hover {
            background: #66ff66;
        }

        /* 載入畫面 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            z-index: 150;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-progress {
            width: 300px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            margin-top: 20px;
            overflow: hidden;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .hidden {
            display: none !important;
        }

        /* 武器商店界面 */
        #weaponShop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #weaponShop h2 {
            font-size: 36px;
            margin-bottom: 30px;
            color: #ffff44;
        }

        .weapon-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            max-width: 800px;
        }

        .weapon-item {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid #666;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .weapon-item:hover {
            border-color: #ffff44;
            background: rgba(255, 255, 255, 0.2);
        }

        .weapon-item.unlocked {
            border-color: #44ff44;
        }

        .weapon-item.current {
            border-color: #ff4444;
            background: rgba(255, 68, 68, 0.2);
        }

        .weapon-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .weapon-stats {
            font-size: 14px;
            margin-bottom: 10px;
        }

        .weapon-unlock {
            font-size: 16px;
            color: #ffff44;
        }

        #closeShop {
            margin-top: 30px;
            padding: 15px 30px;
            font-size: 20px;
            background: #666;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            pointer-events: auto;
        }

        #closeShop:hover {
            background: #888;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <!-- HUD 界面 -->
        <div id="hud">
            <div id="crosshair"></div>
            <!-- 十字準心 -->
            <div id="crosshair"></div>

            <!-- 瞄準鏡 -->
            <div id="scope">
                <div class="scope-marks">
                    <div class="scope-mark horizontal top"></div>
                    <div class="scope-mark horizontal bottom"></div>
                    <div class="scope-mark vertical left"></div>
                    <div class="scope-mark vertical right"></div>
                </div>
            </div>

            <!-- 血量條 -->
            <div id="healthBar">
                <div id="healthFill" style="width: 100%;"></div>
            </div>

            <!-- 彈藥顯示 -->
            <div id="ammoDisplay">
                <span id="currentAmmo">12</span>/<span id="maxAmmo">12</span>
            </div>

            <!-- 遊戲狀態 -->
            <div id="gameStatus">
                <div>分數: <span id="scoreValue">0</span></div>
                <div><span class="weapon-icon" id="currentWeaponIcon">🔫</span><span id="weaponInfo">手槍</span></div>
                <div>敵人: <span id="enemyCount">0</span></div>
                <div>FPS: <span id="fpsDisplay">60</span></div>
            </div>

            <!-- 武器選擇器 -->
            <div id="weaponSelector">
                <div class="weapon-slot active" id="weapon-0">
                    <span class="weapon-icon">🔫</span>
                    <span class="weapon-name">手槍</span>
                    <span class="weapon-key">1</span>
                </div>
                <div class="weapon-slot locked" id="weapon-1">
                    <span class="weapon-icon">💥</span>
                    <span class="weapon-name">霰彈槍</span>
                    <span class="weapon-key">2</span>
                </div>
                <div class="weapon-slot locked" id="weapon-2">
                    <span class="weapon-icon">🔥</span>
                    <span class="weapon-name">突擊步槍</span>
                    <span class="weapon-key">3</span>
                </div>
                <div class="weapon-slot locked" id="weapon-3">
                    <span class="weapon-icon">⚡</span>
                    <span class="weapon-name">狙擊槍</span>
                    <span class="weapon-key">4</span>
                </div>
            </div>

            <div id="stats">
                <div id="resources">
                    廢料: <span id="scrapValue">0</span> |
                    醫療包: <span id="medkitValue">0</span>
                </div>
                <div id="nearbyItems">
                    附近物資: <span id="nearbyItemsText">無</span>
                </div>
            </div>
            <div id="score">分數: <span id="scoreValue">0</span></div>
        </div>

        <!-- 開始菜單 -->
        <div id="startMenu">
            <h1>🧟 世界末日生存射擊 🧟</h1>
            <div style="margin-bottom: 20px; font-size: 16px; text-align: left; max-width: 600px;">
                <p><strong>🎮 遊戲說明：</strong></p>
                <p>• WASD 移動，空格鍵跳躍，鼠標控制視角，左鍵射擊</p>
                <p>• 右鍵按住開啟瞄準鏡（提高精度和傷害）</p>
                <p>• R 重新裝彈，Q 切換武器，Tab 打開武器商店</p>
                <p>• 1-4 數字鍵快速切換武器</p>
                <p>• T 生成測試目標（調試用）</p>

                <p><strong>🧟 遊戲機制：</strong></p>
                <p>• 喪屍只在一定距離內才會發現並追逐你</p>
                <p>• 追逐中的喪屍頭頂會有紅色指示器</p>
                <p>• 受傷的敵人會顯示血量條</p>
                <p>• 彈藥用完會自動重新裝彈</p>
                <p>• 收集彈藥、醫療包和廢料來維持生存</p>

                <p><strong>🔫 武器解鎖：</strong></p>
                <p>🔫 手槍(0分) → 💥 霰彈槍(500分) → 🔥 突擊步槍(1500分) → ⚡ 狙擊槍(3000分)</p>

                <p><strong>💡 提示：</strong></p>
                <p>• 使用十字準心精確瞄準</p>
                <p>• 注意右上角的敵人數量和FPS</p>
                <p>• 血量條會根據生命值變色</p>
            </div>
            <button id="startButton">開始遊戲</button>
        </div>

        <!-- 遊戲結束畫面 -->
        <div id="gameOver">
            <h2>遊戲結束</h2>
            <div id="finalScore">最終分數: 0</div>
            <button id="restartButton">重新開始</button>
        </div>

        <!-- 武器商店 -->
        <div id="weaponShop">
            <h2>🔫 武器商店 🔫</h2>
            <div class="weapon-list" id="weaponList">
                <!-- 武器項目將由JavaScript動態生成 -->
            </div>
            <button id="closeShop">關閉商店 (Tab)</button>
        </div>

        <!-- 載入畫面 -->
        <div id="loading">
            <div class="loading-spinner"></div>
            <div>🎮 載入遊戲中...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loadingBar"></div>
            </div>
            <div id="loadingText" style="margin-top: 10px; font-size: 16px;">正在初始化...</div>
        </div>
    </div>

    <!-- Three.js 庫 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>

    <!-- 遊戲主程式 -->
    <script src="js/game.js"></script>

    <!-- 錯誤處理和網絡檢查 -->
    <script>
        // 檢查網絡連接
        function checkConnection() {
            if (!navigator.onLine) {
                const offlineDiv = document.createElement('div');
                offlineDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(255, 165, 0, 0.9);
                    color: white;
                    padding: 10px 15px;
                    border-radius: 5px;
                    z-index: 10000;
                    font-size: 14px;
                `;
                offlineDiv.textContent = '⚠️ 網絡連接中斷';
                document.body.appendChild(offlineDiv);

                setTimeout(() => {
                    if (document.body.contains(offlineDiv)) {
                        document.body.removeChild(offlineDiv);
                    }
                }, 5000);
            }
        }

        // 監聽網絡狀態
        window.addEventListener('online', () => {
            console.log('✅ 網絡連接恢復');
        });

        window.addEventListener('offline', () => {
            console.log('❌ 網絡連接中斷');
            checkConnection();
        });

        // 錯誤處理
        window.addEventListener('error', function(e) {
            console.error('遊戲錯誤:', e.error);
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 10000;
                max-width: 80%;
                text-align: center;
            `;
            errorDiv.innerHTML = `
                <h3>遊戲載入錯誤</h3>
                <p>請刷新頁面重試</p>
                <p style="font-size: 12px; margin-top: 10px;">錯誤: ${e.message}</p>
                <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px;">重新載入</button>
            `;
            document.body.appendChild(errorDiv);
        });

        // 檢查Three.js載入
        setTimeout(() => {
            if (typeof THREE === 'undefined') {
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(255, 0, 0, 0.9);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    z-index: 10000;
                    text-align: center;
                `;
                errorDiv.innerHTML = `
                    <h3>Three.js 載入失敗</h3>
                    <p>請檢查網絡連接並重新載入</p>
                    <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px;">重新載入</button>
                `;
                document.body.appendChild(errorDiv);
            }
        }, 3000);
    </script>
</body>
</html>
