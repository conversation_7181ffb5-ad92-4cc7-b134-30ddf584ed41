<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 瞄準鏡功能測試</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #222;
            color: white;
            overflow: hidden;
        }

        #testArea {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(to bottom, #87CEEB, #98FB98);
            cursor: crosshair;
        }

        /* 十字準心 */
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
            transition: all 0.3s ease;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        /* 瞄準鏡 */
        #scope {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 4px solid #333;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            pointer-events: none;
            z-index: 200;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #scope.active {
            opacity: 1;
        }

        #scope::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff0000;
            transform: translateY(-50%);
        }

        #scope::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff0000;
            transform: translateX(-50%);
        }

        /* 瞄準鏡刻度 */
        .scope-marks {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .scope-mark {
            position: absolute;
            background: #ff0000;
        }

        .scope-mark.horizontal {
            width: 30px;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .scope-mark.vertical {
            width: 2px;
            height: 30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .scope-mark.top { top: 15%; }
        .scope-mark.bottom { bottom: 15%; }
        .scope-mark.left { left: 15%; }
        .scope-mark.right { right: 15%; }

        /* 瞄準模式下的效果 */
        body.scoped #crosshair {
            opacity: 0.3;
        }

        body.scoped {
            background: #000;
        }

        /* 狀態顯示 */
        #status {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            z-index: 300;
        }

        .status-item {
            margin: 5px 0;
        }

        .active-status {
            color: #00ff00;
        }

        .inactive-status {
            color: #ff6666;
        }

        /* 目標 */
        .target {
            position: absolute;
            width: 50px;
            height: 50px;
            background: #ff0000;
            border-radius: 50%;
            border: 3px solid #fff;
            cursor: pointer;
        }

        .target::before {
            content: '🎯';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div id="testArea">
        <div id="crosshair"></div>
        
        <!-- 瞄準鏡 -->
        <div id="scope">
            <div class="scope-marks">
                <div class="scope-mark horizontal top"></div>
                <div class="scope-mark horizontal bottom"></div>
                <div class="scope-mark vertical left"></div>
                <div class="scope-mark vertical right"></div>
            </div>
        </div>
        
        <!-- 狀態顯示 -->
        <div id="status">
            <h3>🔍 瞄準鏡功能測試</h3>
            <div class="status-item">右鍵狀態: <span id="rightClickStatus" class="inactive-status">未按下</span></div>
            <div class="status-item">瞄準鏡: <span id="scopeStatus" class="inactive-status">關閉</span></div>
            <div class="status-item">命中目標: <span id="hitCount">0</span></div>
            <div style="margin-top: 15px; font-size: 14px; color: #ccc;">
                <p>• 右鍵按住 = 開啟瞄準鏡</p>
                <p>• 左鍵點擊 = 射擊目標</p>
                <p>• 移動鼠標 = 瞄準</p>
            </div>
        </div>
    </div>

    <script>
        let isScoped = false;
        let hitCount = 0;
        
        // 創建隨機目標
        function createTargets() {
            for (let i = 0; i < 5; i++) {
                const target = document.createElement('div');
                target.className = 'target';
                target.style.left = Math.random() * (window.innerWidth - 50) + 'px';
                target.style.top = Math.random() * (window.innerHeight - 50) + 'px';
                
                target.addEventListener('click', () => {
                    hitCount++;
                    document.getElementById('hitCount').textContent = hitCount;
                    
                    // 重新定位目標
                    target.style.left = Math.random() * (window.innerWidth - 50) + 'px';
                    target.style.top = Math.random() * (window.innerHeight - 50) + 'px';
                    
                    console.log(`🎯 命中目標！總命中: ${hitCount}`);
                });
                
                document.getElementById('testArea').appendChild(target);
            }
        }
        
        // 瞄準鏡切換
        function toggleScope(enable) {
            isScoped = enable;
            const scope = document.getElementById('scope');
            const crosshair = document.getElementById('crosshair');
            const body = document.body;
            const scopeStatus = document.getElementById('scopeStatus');
            
            if (enable) {
                scope.classList.add('active');
                crosshair.classList.add('scoped');
                body.classList.add('scoped');
                scopeStatus.textContent = '開啟';
                scopeStatus.className = 'active-status';
                console.log('🔍 瞄準鏡啟動');
            } else {
                scope.classList.remove('active');
                crosshair.classList.remove('scoped');
                body.classList.remove('scoped');
                scopeStatus.textContent = '關閉';
                scopeStatus.className = 'inactive-status';
                console.log('🔍 瞄準鏡關閉');
            }
        }
        
        // 事件監聽
        document.addEventListener('mousedown', (e) => {
            if (e.button === 2) { // 右鍵
                toggleScope(true);
                document.getElementById('rightClickStatus').textContent = '按下';
                document.getElementById('rightClickStatus').className = 'active-status';
            }
            e.preventDefault();
        });
        
        document.addEventListener('mouseup', (e) => {
            if (e.button === 2) { // 右鍵
                toggleScope(false);
                document.getElementById('rightClickStatus').textContent = '未按下';
                document.getElementById('rightClickStatus').className = 'inactive-status';
            }
        });
        
        // 禁用右鍵菜單
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
        
        // 初始化
        window.addEventListener('load', () => {
            createTargets();
            console.log('🎮 瞄準鏡測試頁面載入完成');
            console.log('📝 使用右鍵測試瞄準鏡功能');
        });
    </script>
</body>
</html>
