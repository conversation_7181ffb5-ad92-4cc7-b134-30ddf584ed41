// 3D 第一人稱射擊遊戲主程式
class FPSGame {
    constructor() {
        // 遊戲狀態
        this.gameState = 'menu'; // menu, playing, gameOver
        this.score = 0;
        this.health = 100;
        this.isInitialized = false;
        this.ammo = 30;
        this.maxAmmo = 30;
        
        // Three.js 核心組件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();
        
        // 控制相關
        this.controls = {
            moveForward: false,
            moveBackward: false,
            moveLeft: false,
            moveRight: false,
            canJump: false,
            isRunning: false,
            isCrouching: false,
            isScoped: false
        };
        
        // 鼠標控制
        this.mouseX = 0;
        this.mouseY = 0;
        this.isPointerLocked = false;
        
        // 遊戲對象
        this.player = {
            position: new THREE.Vector3(0, 2, 0), // 降低玩家高度
            velocity: new THREE.Vector3(0, 0, 0),
            direction: new THREE.Vector3(0, 0, -1),
            speed: 30, // 降低基礎移動速度
            runSpeed: 45, // 降低跑步速度
            crouchSpeed: 15, // 降低蹲下速度
            jumpSpeed: 30, // 增加跳躍力度
            height: 2, // 縮小玩家高度
            crouchHeight: 1
        };
        
        this.enemies = [];
        this.bullets = [];
        this.particles = [];
        
        // 武器系統
        this.weapons = this.initWeapons();
        this.currentWeaponIndex = 0;
        this.currentWeapon = this.weapons[this.currentWeaponIndex];
        this.lastShotTime = 0;

        // 資源和升級
        this.resources = {
            scrap: 0, // 廢料（用於升級）
            medkits: 0 // 醫療包
        };
        this.weaponMesh = null;
        
        // 性能監控
        this.frameCount = 0;
        this.lastFPSTime = Date.now();
        this.fps = 60;

        // 初始化
        this.init();
    }

    // 初始化武器系統
    initWeapons() {
        return [
            {
                name: '手槍',
                type: 'pistol',
                icon: '🔫',
                damage: 35,
                fireRate: 250,
                maxAmmo: 12,
                currentAmmo: 12,
                reloadTime: 1500,
                accuracy: 0.95,
                unlockScore: 0,
                description: '可靠的起始武器，彈藥充足但威力有限'
            },
            {
                name: '霰彈槍',
                type: 'shotgun',
                icon: '💥',
                damage: 80,
                fireRate: 700,
                maxAmmo: 6,
                currentAmmo: 6,
                reloadTime: 2000,
                accuracy: 0.7,
                unlockScore: 500,
                description: '近距離殺傷力極強，但射程有限'
            },
            {
                name: '突擊步槍',
                type: 'assault',
                icon: '🔥',
                damage: 45,
                fireRate: 120,
                maxAmmo: 30,
                currentAmmo: 30,
                reloadTime: 2500,
                accuracy: 0.85,
                unlockScore: 1500,
                description: '平衡的全能武器，適合中距離戰鬥'
            },
            {
                name: '狙擊槍',
                type: 'sniper',
                icon: '⚡',
                damage: 150,
                fireRate: 1000,
                maxAmmo: 5,
                currentAmmo: 5,
                reloadTime: 3000,
                accuracy: 0.98,
                unlockScore: 3000,
                description: '一擊必殺的遠程武器，但射速較慢'
            }
        ];
    }
    
    init() {
        try {
            this.updateLoadingProgress(10, '🔧 初始化渲染器...');
            this.setupRenderer();

            this.updateLoadingProgress(20, '🌍 初始化場景...');
            this.setupScene();

            this.updateLoadingProgress(30, '📷 初始化相機...');
            this.setupCamera();

            this.updateLoadingProgress(40, '💡 初始化光照...');
            this.setupLights();

            this.updateLoadingProgress(60, '🏗️ 創建環境...');
            this.setupEnvironment();

            this.updateLoadingProgress(80, '🔫 創建武器...');
            this.setupWeapon();

            this.updateLoadingProgress(90, '⌨️ 設置事件監聽...');
            this.setupEventListeners();

            this.updateLoadingProgress(95, '🎮 設置UI...');
            this.setupUI();

            this.updateLoadingProgress(100, '✅ 載入完成！');
            this.isInitialized = true;
            console.log('✅ 遊戲初始化完成');

            // 延遲隱藏載入畫面
            setTimeout(() => {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.classList.add('hidden');
                }
            }, 500);

        } catch (error) {
            console.error('❌ 初始化失敗:', error);
            this.updateLoadingProgress(0, '❌ 載入失敗');
            throw error;
        }
    }

    updateLoadingProgress(percent, text) {
        const loadingBar = document.getElementById('loadingBar');
        const loadingText = document.getElementById('loadingText');

        if (loadingBar) {
            loadingBar.style.width = `${percent}%`;
        }

        if (loadingText) {
            loadingText.textContent = text;
        }

        console.log(`載入進度: ${percent}% - ${text}`);
    }
    
    setupRenderer() {
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // 較亮的天空
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 300, 1200); // 更遠的霧氣距離以適應高建築
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            80, // 增加視野角度以更好地看到高建築
            window.innerWidth / window.innerHeight,
            0.1,
            1500 // 增加遠裁剪面以看到更遠的建築
        );
        this.camera.position.copy(this.player.position);
    }
    
    setupLights() {
        // 環境光（較亮）
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // 方向光（明亮的陽光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
    }

    setupWeapon() {
        this.createWeaponModel();
    }

    createWeaponModel() {
        // 移除舊武器模型
        if (this.weaponMesh) {
            this.scene.remove(this.weaponMesh);
        }

        const weapon = this.currentWeapon;
        let weaponGroup = new THREE.Group();

        switch (weapon.type) {
            case 'pistol':
                weaponGroup = this.createPistolModel();
                break;
            case 'shotgun':
                weaponGroup = this.createShotgunModel();
                break;
            case 'assault':
                weaponGroup = this.createAssaultRifleModel();
                break;
            case 'sniper':
                weaponGroup = this.createSniperRifleModel();
                break;
        }

        // 設置武器位置（第一人稱視角）
        weaponGroup.position.set(0.3, -0.2, -0.5);
        weaponGroup.scale.set(0.1, 0.1, 0.1);

        this.weaponMesh = weaponGroup;
        this.camera.add(this.weaponMesh);
    }

    createPistolModel() {
        const group = new THREE.Group();

        // 槍身
        const bodyGeometry = new THREE.BoxGeometry(2, 1, 8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.z = -2;
        group.add(body);

        // 槍管
        const barrelGeometry = new THREE.CylinderGeometry(0.2, 0.2, 4, 8);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0, 0.3, -4);
        group.add(barrel);

        // 握把
        const gripGeometry = new THREE.BoxGeometry(1, 3, 1);
        const gripMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const grip = new THREE.Mesh(gripGeometry, gripMaterial);
        grip.position.set(0, -1.5, 1);
        group.add(grip);

        return group;
    }

    createShotgunModel() {
        const group = new THREE.Group();

        // 槍身（更粗更長）
        const bodyGeometry = new THREE.BoxGeometry(2.5, 1.5, 12);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.z = -3;
        group.add(body);

        // 槍管（更粗）
        const barrelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 6, 8);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0, 0.5, -6);
        group.add(barrel);

        // 握把
        const gripGeometry = new THREE.BoxGeometry(1.2, 3.5, 1.2);
        const gripMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const grip = new THREE.Mesh(gripGeometry, gripMaterial);
        grip.position.set(0, -1.8, 2);
        group.add(grip);

        // 槍托
        const stockGeometry = new THREE.BoxGeometry(1.5, 1, 4);
        const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const stock = new THREE.Mesh(stockGeometry, stockMaterial);
        stock.position.set(0, 0, 5);
        group.add(stock);

        return group;
    }

    createAssaultRifleModel() {
        const group = new THREE.Group();

        // 槍身
        const bodyGeometry = new THREE.BoxGeometry(2, 1.2, 10);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F2F });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.z = -2;
        group.add(body);

        // 槍管
        const barrelGeometry = new THREE.CylinderGeometry(0.25, 0.25, 5, 8);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0, 0.4, -5);
        group.add(barrel);

        // 握把
        const gripGeometry = new THREE.BoxGeometry(1, 3, 1);
        const gripMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const grip = new THREE.Mesh(gripGeometry, gripMaterial);
        grip.position.set(0, -1.5, 1);
        group.add(grip);

        // 槍托
        const stockGeometry = new THREE.BoxGeometry(1.5, 1, 3);
        const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const stock = new THREE.Mesh(stockGeometry, stockMaterial);
        stock.position.set(0, 0, 4);
        group.add(stock);

        // 彈匣
        const magGeometry = new THREE.BoxGeometry(0.8, 2, 1.5);
        const magMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const mag = new THREE.Mesh(magGeometry, magMaterial);
        mag.position.set(0, -1.5, -1);
        group.add(mag);

        return group;
    }

    createSniperRifleModel() {
        const group = new THREE.Group();

        // 槍身（更長）
        const bodyGeometry = new THREE.BoxGeometry(2, 1, 15);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.z = -4;
        group.add(body);

        // 槍管（更長更細）
        const barrelGeometry = new THREE.CylinderGeometry(0.2, 0.2, 8, 8);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0, 0.3, -8);
        group.add(barrel);

        // 瞄準鏡
        const scopeGeometry = new THREE.CylinderGeometry(0.3, 0.3, 4, 8);
        const scopeMaterial = new THREE.MeshLambertMaterial({ color: 0x111111 });
        const scope = new THREE.Mesh(scopeGeometry, scopeMaterial);
        scope.rotation.z = Math.PI / 2;
        scope.position.set(0, 1, -2);
        group.add(scope);

        // 握把
        const gripGeometry = new THREE.BoxGeometry(1, 3, 1);
        const gripMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const grip = new THREE.Mesh(gripGeometry, gripMaterial);
        grip.position.set(0, -1.5, 2);
        group.add(grip);

        // 槍托
        const stockGeometry = new THREE.BoxGeometry(1.8, 1.2, 4);
        const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const stock = new THREE.Mesh(stockGeometry, stockMaterial);
        stock.position.set(0, 0, 6);
        group.add(stock);

        return group;
    }
    
    setupEnvironment() {
        // 創建天空盒
        this.createSkybox();

        // 創建地面（更大的地圖）
        const groundGeometry = new THREE.PlaneGeometry(600, 600, 100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x90EE90, // 較亮的綠色草地
            wireframe: false
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);

        // 創建城市道路
        this.createCityRoads();

        // 創建一些障礙物/建築
        this.createObstacles();

        // 創建邊界牆
        this.createBoundaryWalls();

        // 創建環境資源點
        this.createEnvironmentResources();
    }

    createSkybox() {
        const skyGeometry = new THREE.SphereGeometry(1000, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB, // 較亮的天空藍
            side: THREE.BackSide
        });
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);

        // 添加白色雲朵效果
        this.createClouds();
    }

    createClouds() {
        const cloudGeometry = new THREE.SphereGeometry(15, 8, 8);
        const cloudMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff, // 白色雲朵
            transparent: true,
            opacity: 0.8
        });

        for (let i = 0; i < 30; i++) {
            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
            cloud.position.set(
                (Math.random() - 0.5) * 800,
                80 + Math.random() * 100,
                (Math.random() - 0.5) * 800
            );
            cloud.scale.set(
                1 + Math.random(),
                0.5 + Math.random() * 0.5,
                1 + Math.random()
            );
            this.scene.add(cloud);
        }
    }

    createBoundaryWalls() {
        const wallGeometry = new THREE.BoxGeometry(6, 25, 6);
        const wallMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });

        // 創建邊界牆（更大的地圖）
        const wallPositions = [
            // 北牆
            ...Array.from({length: 50}, (_, i) => [i * 12 - 294, 12.5, -300]),
            // 南牆
            ...Array.from({length: 50}, (_, i) => [i * 12 - 294, 12.5, 300]),
            // 東牆
            ...Array.from({length: 50}, (_, i) => [300, 12.5, i * 12 - 294]),
            // 西牆
            ...Array.from({length: 50}, (_, i) => [-300, 12.5, i * 12 - 294])
        ];

        wallPositions.forEach(([x, y, z]) => {
            const wall = new THREE.Mesh(wallGeometry, wallMaterial);
            wall.position.set(x, y, z);
            wall.castShadow = true;
            wall.receiveShadow = true;
            this.scene.add(wall);
        });
    }

    createCityRoads() {
        // 創建主要道路網格
        const roadWidth = 12;
        const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

        // 水平道路
        for (let i = -3; i <= 3; i++) {
            const roadGeometry = new THREE.PlaneGeometry(600, roadWidth);
            const road = new THREE.Mesh(roadGeometry, roadMaterial);
            road.rotation.x = -Math.PI / 2;
            road.position.set(0, 0.1, i * 100);
            road.receiveShadow = true;
            this.scene.add(road);

            // 道路標線
            this.addRoadMarkings(road, 600, roadWidth, 'horizontal');
        }

        // 垂直道路
        for (let i = -3; i <= 3; i++) {
            const roadGeometry = new THREE.PlaneGeometry(roadWidth, 600);
            const road = new THREE.Mesh(roadGeometry, roadMaterial);
            road.rotation.x = -Math.PI / 2;
            road.position.set(i * 100, 0.1, 0);
            road.receiveShadow = true;
            this.scene.add(road);

            // 道路標線
            this.addRoadMarkings(road, roadWidth, 600, 'vertical');
        }

        // 添加一些廢棄車輛
        this.createAbandonedVehicles();

        // 添加城市細節
        this.createCityDetails();
    }

    addRoadMarkings(road, width, length, direction) {
        const markingMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });

        if (direction === 'horizontal') {
            // 中央分隔線
            const lineGeometry = new THREE.PlaneGeometry(width, 0.3);
            const line = new THREE.Mesh(lineGeometry, markingMaterial);
            line.rotation.x = -Math.PI / 2;
            line.position.set(0, 0.11, 0);
            road.add(line);

            // 虛線標記
            for (let x = -width/2 + 10; x < width/2 - 10; x += 20) {
                const dashGeometry = new THREE.PlaneGeometry(8, 0.2);
                const dash = new THREE.Mesh(dashGeometry, markingMaterial);
                dash.rotation.x = -Math.PI / 2;
                dash.position.set(x, 0.11, 3);
                road.add(dash);

                const dash2 = new THREE.Mesh(dashGeometry, markingMaterial);
                dash2.rotation.x = -Math.PI / 2;
                dash2.position.set(x, 0.11, -3);
                road.add(dash2);
            }
        } else {
            // 垂直道路標線
            const lineGeometry = new THREE.PlaneGeometry(0.3, length);
            const line = new THREE.Mesh(lineGeometry, markingMaterial);
            line.rotation.x = -Math.PI / 2;
            line.position.set(0, 0.11, 0);
            road.add(line);
        }
    }

    createAbandonedVehicles() {
        // 創建廢棄車輛
        for (let i = 0; i < 15; i++) {
            const vehicleGroup = new THREE.Group();

            // 車身
            const bodyGeometry = new THREE.BoxGeometry(4, 1.5, 8);
            const bodyMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(Math.random(), 0.5, 0.3)
            });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 1.5;
            vehicleGroup.add(body);

            // 車頂
            const roofGeometry = new THREE.BoxGeometry(3.5, 1, 6);
            const roofMaterial = new THREE.MeshLambertMaterial({
                color: bodyMaterial.color.clone().multiplyScalar(0.8)
            });
            const roof = new THREE.Mesh(roofGeometry, roofMaterial);
            roof.position.y = 2.5;
            vehicleGroup.add(roof);

            // 輪子
            const wheelGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.5, 8);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });

            const positions = [
                [-1.8, 0.8, 2.5],
                [1.8, 0.8, 2.5],
                [-1.8, 0.8, -2.5],
                [1.8, 0.8, -2.5]
            ];

            positions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.position.set(pos[0], pos[1], pos[2]);
                wheel.rotation.z = Math.PI / 2;
                vehicleGroup.add(wheel);
            });

            // 隨機放置在道路附近
            const roadPositions = [
                [0, 0, 100], [0, 0, -100], [0, 0, 200], [0, 0, -200],
                [100, 0, 0], [-100, 0, 0], [200, 0, 0], [-200, 0, 0]
            ];

            const roadPos = roadPositions[Math.floor(Math.random() * roadPositions.length)];
            vehicleGroup.position.set(
                roadPos[0] + (Math.random() - 0.5) * 20,
                0,
                roadPos[2] + (Math.random() - 0.5) * 20
            );

            // 隨機旋轉
            vehicleGroup.rotation.y = Math.random() * Math.PI * 2;

            vehicleGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(vehicleGroup);
        }
    }

    createCityDetails() {
        // 創建路燈（減少數量）
        for (let i = 0; i < 15; i++) {
            const lampGroup = new THREE.Group();

            // 燈柱
            const poleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 8, 8);
            const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.y = 4;
            lampGroup.add(pole);

            // 燈頭
            const lampGeometry = new THREE.SphereGeometry(1, 8, 8);
            const lampMaterial = new THREE.MeshBasicMaterial({
                color: Math.random() > 0.5 ? 0xffff88 : 0x333333
            });
            const lamp = new THREE.Mesh(lampGeometry, lampMaterial);
            lamp.position.y = 8.5;
            lampGroup.add(lamp);

            // 隨機放置在道路旁
            lampGroup.position.set(
                (Math.random() - 0.5) * 400,
                0,
                (Math.random() - 0.5) * 400
            );

            lampGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(lampGroup);
        }

        // 創建垃圾桶和其他城市家具（減少數量）
        for (let i = 0; i < 12; i++) {
            const furnitureGroup = new THREE.Group();

            if (Math.random() > 0.5) {
                // 垃圾桶
                const binGeometry = new THREE.CylinderGeometry(1, 1, 2, 8);
                const binMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
                const bin = new THREE.Mesh(binGeometry, binMaterial);
                bin.position.y = 1;
                furnitureGroup.add(bin);

                // 垃圾桶蓋
                const lidGeometry = new THREE.CylinderGeometry(1.1, 1.1, 0.2, 8);
                const lidMaterial = new THREE.MeshLambertMaterial({ color: 0x555555 });
                const lid = new THREE.Mesh(lidGeometry, lidMaterial);
                lid.position.y = 2.1;
                furnitureGroup.add(lid);
            } else {
                // 長椅
                const benchGeometry = new THREE.BoxGeometry(4, 0.3, 1);
                const benchMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const bench = new THREE.Mesh(benchGeometry, benchMaterial);
                bench.position.y = 1;
                furnitureGroup.add(bench);

                // 椅背
                const backGeometry = new THREE.BoxGeometry(4, 1.5, 0.2);
                const back = new THREE.Mesh(backGeometry, benchMaterial);
                back.position.set(0, 1.8, -0.4);
                furnitureGroup.add(back);

                // 椅腿
                for (let j = 0; j < 4; j++) {
                    const legGeometry = new THREE.BoxGeometry(0.2, 1, 0.2);
                    const leg = new THREE.Mesh(legGeometry, benchMaterial);
                    leg.position.set(
                        j < 2 ? -1.8 : 1.8,
                        0.5,
                        j % 2 === 0 ? 0.4 : -0.4
                    );
                    furnitureGroup.add(leg);
                }
            }

            furnitureGroup.position.set(
                (Math.random() - 0.5) * 450,
                0,
                (Math.random() - 0.5) * 450
            );

            furnitureGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(furnitureGroup);
        }
    }

    createObstacles() {
        // 創建不同類型的障礙物
        this.createBuildings();
        this.createTrees();
        this.createRocks();
    }

    createBuildings() {
        // 創建城市街區
        this.createCityBlocks();

        // 創建摩天大樓
        this.createSkyscrapers();

        // 創建商業建築
        this.createCommercialBuildings();

        // 創建住宅區
        this.createResidentialArea();
    }

    createCityBlocks() {
        // 創建規劃的城市街區
        const blockSize = 80;
        const streetWidth = 20;

        for (let x = -2; x <= 2; x++) {
            for (let z = -2; z <= 2; z++) {
                if (x === 0 && z === 0) continue; // 中心留空給玩家

                const blockX = x * (blockSize + streetWidth);
                const blockZ = z * (blockSize + streetWidth);

                this.createCityBlock(blockX, blockZ, blockSize);
            }
        }
    }

    createCityBlock(centerX, centerZ, blockSize) {
        const buildingCount = 3 + Math.floor(Math.random() * 3); // 稍微減少數量但增加體積

        for (let i = 0; i < buildingCount; i++) {
            const buildingGroup = new THREE.Group();

            const width = 25 + Math.random() * 35; // 大幅增加寬度
            const height = 35 + Math.random() * 70; // 大幅增加高度
            const depth = 25 + Math.random() * 35; // 大幅增加深度

            // 主建築體
            const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0, 0, 0.3 + Math.random() * 0.4)
            });
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
            building.position.y = height / 2;
            buildingGroup.add(building);

            // 添加窗戶
            this.addWindows(building, width, height, depth);

            // 隨機位置在街區內
            buildingGroup.position.set(
                centerX + (Math.random() - 0.5) * blockSize * 0.8,
                0,
                centerZ + (Math.random() - 0.5) * blockSize * 0.8
            );

            buildingGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(buildingGroup);
        }
    }

    addWindows(building, width, height, depth) {
        // 簡化窗戶系統，減少幾何體數量
        const windowMaterial = new THREE.MeshBasicMaterial({
            color: Math.random() > 0.7 ? 0xffff88 : 0x333366
        });

        // 只在前面添加窗戶，減少渲染負擔
        for (let y = 8; y < height - 4; y += 6) { // 增加間距
            for (let x = -width/2 + 4; x < width/2 - 4; x += 6) { // 增加間距
                const windowGeometry = new THREE.BoxGeometry(2, 2, 0.2);
                const window1 = new THREE.Mesh(windowGeometry, windowMaterial);
                window1.position.set(x, y, depth/2 + 0.1);
                building.add(window1);
            }
        }
    }

    addBuildingDetails(building, width, height, depth) {
        // 添加建築頂部結構
        const rooftopGeometry = new THREE.BoxGeometry(width * 0.8, 3, depth * 0.8);
        const rooftopMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const rooftop = new THREE.Mesh(rooftopGeometry, rooftopMaterial);
        rooftop.position.y = height/2 + 1.5;
        building.add(rooftop);

        // 添加建築中段裝飾
        if (height > 100) {
            const midGeometry = new THREE.BoxGeometry(width * 1.1, 2, depth * 1.1);
            const midMaterial = new THREE.MeshLambertMaterial({ color: 0x555555 });
            const midSection = new THREE.Mesh(midGeometry, midMaterial);
            midSection.position.y = height * 0.6;
            building.add(midSection);
        }

        // 添加建築底部基座
        const baseGeometry = new THREE.BoxGeometry(width * 1.2, 4, depth * 1.2);
        const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const base = new THREE.Mesh(baseGeometry, baseMaterial);
        base.position.y = -height/2 + 2;
        building.add(base);
    }

    createSkyscrapers() {
        // 創建更高更大的摩天大樓
        for (let i = 0; i < 6; i++) { // 增加數量
            const skyscraperGroup = new THREE.Group();

            const width = 40 + Math.random() * 30; // 增加寬度
            const height = 120 + Math.random() * 100; // 大幅增加高度
            const depth = 40 + Math.random() * 30; // 增加深度

            // 主體
            const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.6, 0.2, 0.4 + Math.random() * 0.3)
            });
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
            building.position.y = height / 2;
            skyscraperGroup.add(building);

            // 添加密集的窗戶
            this.addWindows(building, width, height, depth);

            // 為大型建築添加額外細節
            if (height > 150) {
                this.addBuildingDetails(building, width, height, depth);
            }

            // 頂部天線（更高）
            const antennaHeight = 30 + Math.random() * 20; // 增加天線高度
            const antennaGeometry = new THREE.CylinderGeometry(0.8, 0.8, antennaHeight, 8);
            const antennaMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
            antenna.position.y = height + antennaHeight/2;
            skyscraperGroup.add(antenna);

            // 添加天線頂部的紅燈
            const lightGeometry = new THREE.SphereGeometry(1.5, 8, 8);
            const lightMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.8
            });
            const light = new THREE.Mesh(lightGeometry, lightMaterial);
            light.position.y = height + antennaHeight;
            skyscraperGroup.add(light);

            skyscraperGroup.position.set(
                (Math.random() - 0.5) * 400,
                0,
                (Math.random() - 0.5) * 400
            );

            skyscraperGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(skyscraperGroup);
        }
    }

    createCommercialBuildings() {
        // 創建更大的商業建築
        for (let i = 0; i < 12; i++) { // 增加數量
            const buildingGroup = new THREE.Group();

            const width = 30 + Math.random() * 35; // 增加寬度
            const height = 15 + Math.random() * 25; // 增加高度
            const depth = 20 + Math.random() * 30; // 增加深度

            // 主建築
            const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
            const buildingMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.1, 0.3, 0.5 + Math.random() * 0.3)
            });
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
            building.position.y = height / 2;
            buildingGroup.add(building);

            // 商店招牌（更大更顯眼）
            const signWidth = width * 0.9;
            const signHeight = Math.max(3, height * 0.1);
            const signGeometry = new THREE.BoxGeometry(signWidth, signHeight, 0.8);
            const signMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(Math.random(), 0.8, 0.6)
            });
            const sign = new THREE.Mesh(signGeometry, signMaterial);
            sign.position.set(0, height * 0.85, depth/2 + 0.5);
            buildingGroup.add(sign);

            // 添加招牌邊框
            const frameGeometry = new THREE.BoxGeometry(signWidth + 0.5, signHeight + 0.5, 0.2);
            const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const frame = new THREE.Mesh(frameGeometry, frameMaterial);
            frame.position.set(0, height * 0.85, depth/2 + 0.2);
            buildingGroup.add(frame);

            this.addWindows(building, width, height, depth);

            buildingGroup.position.set(
                (Math.random() - 0.5) * 450,
                0,
                (Math.random() - 0.5) * 450
            );

            buildingGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(buildingGroup);
        }
    }

    createResidentialArea() {
        // 創建更大的住宅區
        for (let i = 0; i < 15; i++) { // 增加數量
            const houseGroup = new THREE.Group();

            const width = 12 + Math.random() * 12; // 增加寬度
            const height = 10 + Math.random() * 15; // 增加高度
            const depth = 12 + Math.random() * 12; // 增加深度

            // 房屋主體
            const houseGeometry = new THREE.BoxGeometry(width, height, depth);
            const houseMaterial = new THREE.MeshLambertMaterial({
                color: new THREE.Color().setHSL(0.05, 0.4, 0.6 + Math.random() * 0.3)
            });
            const house = new THREE.Mesh(houseGeometry, houseMaterial);
            house.position.y = height / 2;
            houseGroup.add(house);

            // 屋頂（更大更高）
            const roofSize = Math.max(width, depth) * 0.9; // 根據房屋大小調整
            const roofHeight = 6 + Math.random() * 4; // 增加屋頂高度
            const roofGeometry = new THREE.ConeGeometry(roofSize, roofHeight, 4);
            const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const roof = new THREE.Mesh(roofGeometry, roofMaterial);
            roof.position.y = height + roofHeight/2;
            roof.rotation.y = Math.PI / 4;
            houseGroup.add(roof);

            // 門（更大）
            const doorWidth = Math.min(width * 0.2, 3);
            const doorHeight = Math.min(height * 0.4, 6);
            const doorGeometry = new THREE.BoxGeometry(doorWidth, doorHeight, 0.2);
            const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
            const door = new THREE.Mesh(doorGeometry, doorMaterial);
            door.position.set(0, doorHeight/2, depth/2 + 0.1);
            houseGroup.add(door);

            // 窗戶（更大更多）
            const windowSize = Math.min(width * 0.15, 2.5);
            const windowGeometry = new THREE.BoxGeometry(windowSize, windowSize, 0.2);
            const windowMaterial = new THREE.MeshBasicMaterial({ color: 0x87CEEB });

            // 左側窗戶
            const window1 = new THREE.Mesh(windowGeometry, windowMaterial);
            window1.position.set(-width/3, height * 0.4, depth/2 + 0.1);
            houseGroup.add(window1);

            // 右側窗戶
            const window2 = new THREE.Mesh(windowGeometry, windowMaterial);
            window2.position.set(width/3, height * 0.4, depth/2 + 0.1);
            houseGroup.add(window2);

            // 如果房屋足夠大，添加更多窗戶
            if (width > 15) {
                const window3 = new THREE.Mesh(windowGeometry, windowMaterial);
                window3.position.set(-width/2 + 2, height * 0.6, depth/2 + 0.1);
                houseGroup.add(window3);

                const window4 = new THREE.Mesh(windowGeometry, windowMaterial);
                window4.position.set(width/2 - 2, height * 0.6, depth/2 + 0.1);
                houseGroup.add(window4);
            }

            houseGroup.position.set(
                (Math.random() - 0.5) * 500,
                0,
                (Math.random() - 0.5) * 500
            );

            houseGroup.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(houseGroup);
        }
    }

    createTrees() {
        // 創建更多樹木
        for (let i = 0; i < 50; i++) {
            const tree = new THREE.Group();

            // 樹幹
            const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.6, 5 + Math.random() * 3, 6);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x3a2f2a }); // 深棕色
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.y = (5 + Math.random() * 3) / 2;
            tree.add(trunk);

            // 隨機添加一些枯枝
            const branchCount = 2 + Math.floor(Math.random() * 4);
            for (let j = 0; j < branchCount; j++) {
                const branchGeometry = new THREE.CylinderGeometry(0.05, 0.1, 1 + Math.random(), 4);
                const branchMaterial = new THREE.MeshLambertMaterial({ color: 0x2a1f1a });
                const branch = new THREE.Mesh(branchGeometry, branchMaterial);

                branch.position.set(
                    (Math.random() - 0.5) * 2,
                    3 + Math.random() * 2,
                    (Math.random() - 0.5) * 2
                );
                branch.rotation.set(
                    Math.random() * Math.PI,
                    Math.random() * Math.PI,
                    Math.random() * Math.PI
                );
                tree.add(branch);
            }

            tree.position.set(
                (Math.random() - 0.5) * 550,
                0,
                (Math.random() - 0.5) * 550
            );

            tree.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                }
            });

            this.scene.add(tree);
        }
    }

    createRocks() {
        const rockMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });

        // 創建更多岩石
        for (let i = 0; i < 30; i++) {
            const rockGeometry = new THREE.DodecahedronGeometry(1 + Math.random() * 2);
            const rock = new THREE.Mesh(rockGeometry, rockMaterial);

            rock.position.set(
                (Math.random() - 0.5) * 560,
                1,
                (Math.random() - 0.5) * 560
            );

            rock.rotation.set(
                Math.random() * Math.PI,
                Math.random() * Math.PI,
                Math.random() * Math.PI
            );

            rock.castShadow = true;
            rock.receiveShadow = true;
            this.scene.add(rock);
        }
    }

    createEnvironmentResources() {
        // 初始化拾取物品數組
        if (!this.pickups) this.pickups = [];

        // 在地圖上隨機放置更多資源
        for (let i = 0; i < 20; i++) {
            const resourceType = Math.random();
            const position = new THREE.Vector3(
                (Math.random() - 0.5) * 500,
                0,
                (Math.random() - 0.5) * 500
            );

            if (resourceType < 0.4) {
                this.createAmmoPickup(position);
            } else if (resourceType < 0.7) {
                this.createHealthPickup(position);
            } else {
                this.createScrapPickup(position);
            }
        }

        // 創建更多彈藥箱（大型資源點）
        for (let i = 0; i < 8; i++) {
            this.createAmmoBox(new THREE.Vector3(
                (Math.random() - 0.5) * 450,
                0,
                (Math.random() - 0.5) * 450
            ));
        }
    }

    createAmmoBox(position) {
        const ammoBoxGroup = new THREE.Group();

        // 主體彈藥箱（更大）
        const boxGeometry = new THREE.BoxGeometry(3, 2, 3);
        const boxMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const box = new THREE.Mesh(boxGeometry, boxMaterial);
        box.position.y = 1;
        ammoBoxGroup.add(box);

        // 添加彈藥標記（更明顯）
        const labelGeometry = new THREE.BoxGeometry(3.2, 0.2, 3.2);
        const labelMaterial = new THREE.MeshBasicMaterial({ color: 0xFFD700 });
        const label = new THREE.Mesh(labelGeometry, labelMaterial);
        label.position.y = 2.1;
        ammoBoxGroup.add(label);

        // 添加文字標記（用小方塊組成"AMMO"）
        const textMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
        const textGeometry = new THREE.BoxGeometry(0.2, 0.3, 0.2);

        // 簡單的"A"形狀
        for (let i = 0; i < 5; i++) {
            const textBlock = new THREE.Mesh(textGeometry, textMaterial);
            textBlock.position.set(-1 + i * 0.5, 2.2, 1.6);
            ammoBoxGroup.add(textBlock);
        }

        // 添加發光邊框
        const glowGeometry = new THREE.BoxGeometry(3.5, 2.5, 3.5);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0xFFD700,
            transparent: true,
            opacity: 0.3,
            wireframe: true
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.position.y = 1;
        ammoBoxGroup.add(glowMesh);

        ammoBoxGroup.position.copy(position);
        ammoBoxGroup.position.y = 2;
        this.scene.add(ammoBoxGroup);

        this.pickups.push({
            mesh: ammoBoxGroup,
            type: 'ammoBox',
            position: ammoBoxGroup.position,
            uses: 5 // 增加使用次數
        });
    }

    setupEventListeners() {
        // 鍵盤事件
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // 鼠標事件 - 使用mousedown/mouseup而不是click來避免衝突
        document.addEventListener('mousedown', (event) => this.onMouseDown(event));
        document.addEventListener('mouseup', (event) => this.onMouseUp(event));
        document.addEventListener('mousemove', (event) => this.onMouseMove(event));
        document.addEventListener('contextmenu', (event) => event.preventDefault()); // 禁用右鍵菜單
        
        // 指針鎖定事件
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
        
        // 窗口大小調整
        window.addEventListener('resize', () => this.onWindowResize());
    }
    
    setupUI() {
        // 開始按鈕
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        // 重新開始按鈕
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });

        // 關閉商店按鈕
        document.getElementById('closeShop').addEventListener('click', () => {
            this.toggleWeaponShop();
        });

        // 武器選擇器點擊事件
        for (let i = 0; i < 4; i++) {
            const weaponSlot = document.getElementById(`weapon-${i}`);
            if (weaponSlot) {
                weaponSlot.addEventListener('click', () => {
                    this.switchWeapon(i);
                });
            }
        }

        // 初始化武器商店
        this.initWeaponShop();
    }

    // 初始化武器商店
    initWeaponShop() {
        const weaponList = document.getElementById('weaponList');
        weaponList.innerHTML = '';

        this.weapons.forEach((weapon, index) => {
            const weaponItem = document.createElement('div');
            weaponItem.className = 'weapon-item';
            weaponItem.innerHTML = `
                <div class="weapon-name">
                    <span style="font-size: 24px; margin-right: 10px;">${weapon.icon}</span>
                    ${weapon.name}
                </div>
                <div class="weapon-stats">
                    傷害: ${weapon.damage} | 射速: ${Math.round(60000/weapon.fireRate)}/分鐘<br>
                    彈匣: ${weapon.maxAmmo} | 精度: ${Math.round(weapon.accuracy*100)}%
                </div>
                <div class="weapon-unlock">
                    ${weapon.unlockScore === 0 ? '已解鎖' : `需要 ${weapon.unlockScore} 分數`}
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #ccc;">
                    ${weapon.description}
                </div>
            `;

            weaponItem.addEventListener('click', () => {
                if (this.score >= weapon.unlockScore) {
                    this.switchWeapon(index);
                    this.updateWeaponShopDisplay();
                }
            });

            weaponList.appendChild(weaponItem);
        });

        this.updateWeaponShopDisplay();
    }

    updateWeaponShopDisplay() {
        const weaponItems = document.querySelectorAll('.weapon-item');
        weaponItems.forEach((item, index) => {
            const weapon = this.weapons[index];

            // 移除所有狀態類
            item.classList.remove('unlocked', 'current');

            // 添加適當的狀態類
            if (this.score >= weapon.unlockScore) {
                item.classList.add('unlocked');
            }

            if (index === this.currentWeaponIndex) {
                item.classList.add('current');
            }
        });
    }

    toggleWeaponShop() {
        const weaponShop = document.getElementById('weaponShop');
        const isVisible = weaponShop.style.display === 'flex';

        if (isVisible) {
            weaponShop.style.display = 'none';
            if (this.gameState === 'playing') {
                document.body.requestPointerLock();
            }
        } else {
            weaponShop.style.display = 'flex';
            this.updateWeaponShopDisplay();
            document.exitPointerLock();
        }
    }
    
    startGame() {
        document.getElementById('startMenu').classList.add('hidden');
        this.gameState = 'playing';
        
        // 請求指針鎖定
        document.body.requestPointerLock();
        
        // 開始遊戲循環
        this.gameLoop();
    }
    
    restartGame() {
        // 重置遊戲狀態
        this.score = 0;
        this.health = 100;
        this.currentWeapon.currentAmmo = this.currentWeapon.maxAmmo;
        this.player.position.set(0, 2, 0);
        this.player.velocity.set(0, 0, 0);
        
        // 清除敵人和子彈
        this.enemies.forEach(enemy => this.scene.remove(enemy.mesh));
        this.bullets.forEach(bullet => this.scene.remove(bullet.mesh));
        this.enemies = [];
        this.bullets = [];
        
        // 更新UI
        this.updateUI();
        
        // 隱藏遊戲結束畫面
        document.getElementById('gameOver').style.display = 'none';
        
        // 開始遊戲
        this.startGame();
    }
    
    gameLoop() {
        if (this.gameState !== 'playing' || !this.isInitialized) return;

        try {
            requestAnimationFrame(() => this.gameLoop());

            const deltaTime = this.clock.getDelta();

            // 限制deltaTime防止遊戲跳躍
            const clampedDeltaTime = Math.min(deltaTime, 0.1);

            this.updatePlayer(clampedDeltaTime);
            this.updateCamera();
            this.updateBullets(clampedDeltaTime);
            this.updateEnemies(clampedDeltaTime);
            this.updateParticles(clampedDeltaTime);
            this.updatePickups(clampedDeltaTime);
            this.checkCollisions();
            this.checkPickupCollisions();
            this.updateUI();
            this.updatePerformance();

            this.renderer.render(this.scene, this.camera);

        } catch (error) {
            console.error('❌ 遊戲循環錯誤:', error);
            this.gameState = 'paused';

            // 顯示錯誤信息
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 10000;
                text-align: center;
            `;
            errorDiv.innerHTML = `
                <h3>遊戲運行錯誤</h3>
                <p>遊戲已暫停</p>
                <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px;">重新載入</button>
            `;
            document.body.appendChild(errorDiv);
        }
    }
    
    updateUI() {
        // 更新血量條
        const healthPercent = Math.max(0, this.health / 100 * 100);
        const healthFill = document.getElementById('healthFill');
        if (healthFill) {
            healthFill.style.width = `${healthPercent}%`;
        }

        // 更新彈藥顯示
        const currentAmmo = document.getElementById('currentAmmo');
        const maxAmmo = document.getElementById('maxAmmo');
        if (currentAmmo) currentAmmo.textContent = this.currentWeapon.currentAmmo;
        if (maxAmmo) maxAmmo.textContent = this.currentWeapon.maxAmmo;

        // 更新遊戲狀態
        const scoreValue = document.getElementById('scoreValue');
        const weaponInfo = document.getElementById('weaponInfo');
        const currentWeaponIcon = document.getElementById('currentWeaponIcon');
        const enemyCount = document.getElementById('enemyCount');
        const fpsDisplay = document.getElementById('fpsDisplay');

        if (scoreValue) scoreValue.textContent = this.score;
        if (weaponInfo) weaponInfo.textContent = this.currentWeapon.name;
        if (currentWeaponIcon) currentWeaponIcon.textContent = this.currentWeapon.icon;
        if (enemyCount) enemyCount.textContent = this.enemies.length;
        if (fpsDisplay) fpsDisplay.textContent = this.fps;

        // 更新資源信息
        const scrapValue = document.getElementById('scrapValue');
        const medkitValue = document.getElementById('medkitValue');
        if (scrapValue) scrapValue.textContent = this.resources.scrap;
        if (medkitValue) medkitValue.textContent = this.resources.medkits;

        // 更新附近物資信息
        this.updateNearbyItems();

        // 更新武器選擇器
        this.updateWeaponSelector();

        // 彈藥不足時的視覺提示
        if (this.currentWeapon.currentAmmo === 0) {
            if (currentAmmo) currentAmmo.style.color = '#ff0000';
        } else if (this.currentWeapon.currentAmmo <= 3) {
            if (currentAmmo) currentAmmo.style.color = '#ffff00';
        } else {
            if (currentAmmo) currentAmmo.style.color = '#ffffff';
        }
    }

    updateWeaponSelector() {
        // 更新武器選擇器的狀態
        for (let i = 0; i < this.weapons.length; i++) {
            const weaponSlot = document.getElementById(`weapon-${i}`);
            if (!weaponSlot) continue;

            const weapon = this.weapons[i];
            const isUnlocked = this.score >= weapon.unlockScore;
            const isActive = this.currentWeaponIndex === i;

            // 更新樣式
            weaponSlot.className = 'weapon-slot';
            if (isActive) {
                weaponSlot.classList.add('active');
            }
            if (!isUnlocked) {
                weaponSlot.classList.add('locked');
            }

            // 更新武器名稱（如果被鎖定則顯示解鎖條件）
            const weaponName = weaponSlot.querySelector('.weapon-name');
            if (weaponName) {
                if (isUnlocked) {
                    weaponName.textContent = weapon.name;
                } else {
                    weaponName.textContent = `${weapon.name} (${weapon.unlockScore}分)`;
                }
            }

            // 更新圖標
            const weaponIcon = weaponSlot.querySelector('.weapon-icon');
            if (weaponIcon) {
                weaponIcon.textContent = weapon.icon;
            }
        }
    }

    showDamageNumber(position, damage) {
        // 將3D位置轉換為屏幕位置
        const vector = position.clone();
        vector.project(this.camera);

        const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
        const y = (vector.y * -0.5 + 0.5) * window.innerHeight;

        // 創建傷害數字元素
        const damageElement = document.createElement('div');
        damageElement.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            color: #ff4444;
            font-size: 24px;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            transform: translate(-50%, -50%);
        `;
        damageElement.textContent = `-${damage}`;

        document.body.appendChild(damageElement);

        // 動畫效果
        let opacity = 1;
        let yOffset = 0;
        const animate = () => {
            opacity -= 0.02;
            yOffset -= 2;

            damageElement.style.opacity = opacity;
            damageElement.style.top = `${y + yOffset}px`;

            if (opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                if (document.body.contains(damageElement)) {
                    document.body.removeChild(damageElement);
                }
            }
        };

        requestAnimationFrame(animate);
    }

    updateNearbyItems() {
        const nearbyItemsText = document.getElementById('nearbyItemsText');
        if (!nearbyItemsText || !this.pickups) return;

        const nearbyItems = [];
        const detectionRange = 30; // 檢測範圍

        for (const pickup of this.pickups) {
            const distance = pickup.position.distanceTo(this.player.position);
            if (distance < detectionRange) {
                let itemName = '';
                switch (pickup.type) {
                    case 'ammo':
                        itemName = '彈藥';
                        break;
                    case 'health':
                        itemName = '醫療包';
                        break;
                    case 'scrap':
                        itemName = '廢料';
                        break;
                    case 'ammoBox':
                        itemName = '彈藥箱';
                        break;
                }
                nearbyItems.push(`${itemName}(${Math.floor(distance)}m)`);
            }
        }

        if (nearbyItems.length > 0) {
            nearbyItemsText.textContent = nearbyItems.join(', ');
            nearbyItemsText.style.color = '#ffff44';
        } else {
            nearbyItemsText.textContent = '無';
            nearbyItemsText.style.color = '#888888';
        }
    }

    updateNearestZombie() {
        if (!this.enemies || this.enemies.length === 0) return;

        let nearestDistance = Infinity;
        for (const enemy of this.enemies) {
            const horizontalDistance = Math.sqrt(
                Math.pow(enemy.position.x - this.player.position.x, 2) +
                Math.pow(enemy.position.z - this.player.position.z, 2)
            );
            if (horizontalDistance < nearestDistance) {
                nearestDistance = horizontalDistance;
            }
        }

        // 在控制台顯示最近喪屍距離（調試用）
        if (nearestDistance < 20) {
            console.log(`最近喪屍距離: ${nearestDistance.toFixed(1)}m`);
        }
    }

    updateJumpStatus() {
        // 在控制台顯示跳躍狀態（調試用）
        const isInAir = this.player.position.y > 2.1;
        if (isInAir && Math.floor(Date.now() / 1000) % 2 === 0) {
            console.log(`🌟 在空中，高度: ${this.player.position.y.toFixed(1)}m`);
        }
    }

    updatePerformance() {
        this.frameCount++;
        const currentTime = Date.now();

        if (currentTime - this.lastFPSTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFPSTime = currentTime;

            // 如果FPS太低，輸出警告
            if (this.fps < 30) {
                console.log(`⚠️ 性能警告：FPS = ${this.fps}`);
            }
        }
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    // 鍵盤事件處理
    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW':
                this.controls.moveForward = true;
                break;
            case 'KeyS':
                this.controls.moveBackward = true;
                break;
            case 'KeyA':
                this.controls.moveLeft = true;
                break;
            case 'KeyD':
                this.controls.moveRight = true;
                break;
            case 'Space':
                if (this.controls.canJump) {
                    this.player.velocity.y = this.player.jumpSpeed;
                    this.controls.canJump = false;
                    console.log('🦘 跳躍！');
                    this.createJumpEffect();
                }
                event.preventDefault();
                break;
            case 'KeyR':
                this.reload();
                break;
            case 'Digit1':
                this.switchWeapon(0);
                break;
            case 'Digit2':
                this.switchWeapon(1);
                break;
            case 'Digit3':
                this.switchWeapon(2);
                break;
            case 'Digit4':
                this.switchWeapon(3);
                break;
            case 'KeyQ':
                this.nextWeapon();
                break;
            case 'Tab':
                this.toggleWeaponShop();
                event.preventDefault();
                break;
            case 'KeyT':
                this.spawnTestTarget();
                break;
        }
    }

    // 武器切換系統
    switchWeapon(weaponIndex) {
        if (weaponIndex >= 0 && weaponIndex < this.weapons.length) {
            const weapon = this.weapons[weaponIndex];

            // 檢查武器是否已解鎖
            if (this.score >= weapon.unlockScore) {
                this.currentWeaponIndex = weaponIndex;
                this.currentWeapon = weapon;
                this.createWeaponModel();
                console.log(`切換到: ${weapon.name}`);
            } else {
                console.log(`${weapon.name} 尚未解鎖！需要 ${weapon.unlockScore} 分數`);
            }
        }
    }

    nextWeapon() {
        let nextIndex = this.currentWeaponIndex;
        let attempts = 0;

        do {
            nextIndex = (nextIndex + 1) % this.weapons.length;
            attempts++;
        } while (this.score < this.weapons[nextIndex].unlockScore && attempts < this.weapons.length);

        if (this.score >= this.weapons[nextIndex].unlockScore) {
            this.switchWeapon(nextIndex);
        }
    }

    // 檢查武器解鎖
    checkWeaponUnlocks() {
        for (let i = 0; i < this.weapons.length; i++) {
            const weapon = this.weapons[i];
            if (this.score >= weapon.unlockScore && i > this.currentWeaponIndex) {
                console.log(`🎉 新武器解鎖: ${weapon.name}！按 ${i + 1} 切換`);
                this.showWeaponUnlockNotification(weapon);
                break;
            }
        }
    }

    showWeaponUnlockNotification(weapon) {
        // 創建武器解鎖通知
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 255, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-size: 24px;
            font-weight: bold;
            z-index: 1000;
            text-align: center;
        `;
        notification.innerHTML = `
            <div>🎉 新武器解鎖！</div>
            <div style="font-size: 32px; margin: 10px 0;">${weapon.icon}</div>
            <div>${weapon.name}</div>
            <div style="font-size: 16px; margin-top: 10px;">${weapon.description}</div>
        `;

        document.body.appendChild(notification);

        // 3秒後移除通知
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }

    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW':
                this.controls.moveForward = false;
                break;
            case 'KeyS':
                this.controls.moveBackward = false;
                break;
            case 'KeyA':
                this.controls.moveLeft = false;
                break;
            case 'KeyD':
                this.controls.moveRight = false;
                break;
        }
    }

    // 鼠標事件處理
    onClick(event) {
        if (this.gameState === 'playing' && this.isPointerLocked) {
            // 只有左鍵才射擊，右鍵用於瞄準
            if (event.button === 0) { // 左鍵
                this.shoot();
                console.log('🔫 左鍵射擊');
            }
            // 阻止右鍵射擊
            event.preventDefault();
        }
    }

    onMouseDown(event) {
        if (this.gameState === 'playing' && this.isPointerLocked) {
            if (event.button === 2) { // 右鍵
                this.toggleScope(true);
                console.log('🔍 右鍵按下 - 開啟瞄準鏡');
                event.preventDefault();
            } else if (event.button === 0) { // 左鍵
                this.shoot();
                console.log('🔫 左鍵按下 - 射擊');
                event.preventDefault();
            }
        }
    }

    onMouseUp(event) {
        if (this.gameState === 'playing' && this.isPointerLocked) {
            if (event.button === 2) { // 右鍵
                this.toggleScope(false);
                console.log('🔍 右鍵釋放 - 關閉瞄準鏡');
                event.preventDefault();
            }
        }
    }

    onMouseMove(event) {
        if (!this.isPointerLocked) return;

        // 瞄準時使用更低的靈敏度
        const sensitivity = this.scopedSensitivity || 0.002;
        this.mouseX -= event.movementX * sensitivity;
        this.mouseY -= event.movementY * sensitivity;

        // 限制垂直視角
        this.mouseY = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.mouseY));
    }

    onPointerLockChange() {
        this.isPointerLocked = document.pointerLockElement === document.body;
    }

    toggleScope(enable) {
        this.controls.isScoped = enable;
        const scope = document.getElementById('scope');
        const body = document.body;

        if (enable) {
            scope.classList.add('active');
            body.classList.add('scoped');
            // 瞄準時降低鼠標靈敏度
            this.scopedSensitivity = 0.0008;
            console.log('🔍 瞄準鏡啟動');
        } else {
            scope.classList.remove('active');
            body.classList.remove('scoped');
            this.scopedSensitivity = null;
            console.log('🔍 瞄準鏡關閉');
        }
    }

    // 玩家更新
    updatePlayer(deltaTime) {
        const velocity = this.player.velocity;
        const position = this.player.position;

        // 重力（調整重力強度讓跳躍更自然）
        velocity.y -= 9.8 * 25 * deltaTime;

        // 移動
        const direction = new THREE.Vector3();

        if (this.controls.moveForward) direction.z -= 1;
        if (this.controls.moveBackward) direction.z += 1;
        if (this.controls.moveLeft) direction.x -= 1;
        if (this.controls.moveRight) direction.x += 1;

        if (direction.length() > 0) {
            direction.normalize();
            direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), this.mouseX);

            velocity.x = direction.x * this.player.speed;
            velocity.z = direction.z * this.player.speed;
        } else {
            velocity.x *= 0.8; // 摩擦力
            velocity.z *= 0.8;
        }

        // 更新位置
        position.add(velocity.clone().multiplyScalar(deltaTime));

        // 地面碰撞檢測
        if (position.y <= 2) { // 調整地面高度
            position.y = 2;
            velocity.y = 0;
            if (!this.controls.canJump) {
                console.log('🏃 著陸');
                this.createLandingEffect();
            }
            this.controls.canJump = true;
        } else {
            // 在空中時不能跳躍
            this.controls.canJump = false;
        }

        // 邊界檢測（更大的地圖）
        position.x = Math.max(-290, Math.min(290, position.x));
        position.z = Math.max(-290, Math.min(290, position.z));
    }

    // 相機更新
    updateCamera() {
        this.camera.position.copy(this.player.position);

        // 應用鼠標旋轉
        this.camera.rotation.order = 'YXZ';
        this.camera.rotation.y = this.mouseX;
        this.camera.rotation.x = this.mouseY;

        // 更新玩家方向
        this.player.direction.set(0, 0, -1);
        this.player.direction.applyQuaternion(this.camera.quaternion);
    }

    // 射擊
    shoot() {
        const currentTime = Date.now();
        const weapon = this.currentWeapon;

        // 如果彈藥用完，自動重新裝彈
        if (weapon.currentAmmo <= 0) {
            this.reload();
            return;
        }

        if (currentTime - this.lastShotTime < weapon.fireRate) {
            return;
        }

        this.lastShotTime = currentTime;
        weapon.currentAmmo--;

        // 根據武器類型創建不同的子彈
        if (weapon.type === 'shotgun') {
            // 霰彈槍發射多顆子彈
            for (let i = 0; i < 5; i++) {
                const bullet = this.createBullet(weapon, i * 0.1 - 0.2);
                this.bullets.push(bullet);
            }
        } else {
            const bullet = this.createBullet(weapon);
            this.bullets.push(bullet);
        }

        // 武器後坐力效果
        this.addRecoil(weapon);

        // 播放射擊音效
        this.playShootSound(weapon.type);

        // 彈藥不足提示
        if (weapon.currentAmmo <= 3 && weapon.currentAmmo > 0) {
            this.showLowAmmoWarning();
        }
    }

    createBullet(weapon, spread = 0) {
        const bulletGeometry = new THREE.SphereGeometry(0.5, 6, 6); // 減少幾何體複雜度
        const bulletMaterial = new THREE.MeshBasicMaterial({
            color: this.controls.isScoped ? 0xffff00 : 0xff0000, // 瞄準時子彈為黃色
            transparent: true,
            opacity: 0.8
        });
        const bulletMesh = new THREE.Mesh(bulletGeometry, bulletMaterial);

        bulletMesh.position.copy(this.camera.position);
        bulletMesh.position.add(this.player.direction.clone().multiplyScalar(1));

        this.scene.add(bulletMesh);

        const scopeText = this.controls.isScoped ? ' (瞄準)' : '';
        console.log(`🔫 發射子彈${scopeText}，位置: (${bulletMesh.position.x.toFixed(1)}, ${bulletMesh.position.y.toFixed(1)}, ${bulletMesh.position.z.toFixed(1)})`);

        // 計算子彈方向（包含精度和散射）
        const direction = this.player.direction.clone();

        // 瞄準時提高精度
        const accuracy = this.controls.isScoped ? Math.min(weapon.accuracy + 0.1, 1.0) : weapon.accuracy;
        const inaccuracy = (1 - accuracy) * 0.1;

        // 瞄準時減少散布
        const finalSpread = this.controls.isScoped ? spread * 0.3 : spread;

        direction.x += (Math.random() - 0.5) * inaccuracy + finalSpread;
        direction.y += (Math.random() - 0.5) * inaccuracy;
        direction.z += (Math.random() - 0.5) * inaccuracy;
        direction.normalize();

        return {
            mesh: bulletMesh,
            direction: direction,
            speed: this.controls.isScoped ? 450 : 300, // 瞄準時子彈速度更快
            life: 5.0,
            damage: this.controls.isScoped ? weapon.damage * 1.3 : weapon.damage // 瞄準時傷害加成
        };
    }

    addRecoil(weapon) {
        // 根據武器類型添加後坐力
        let recoilStrength = 0.02;
        switch (weapon.type) {
            case 'pistol':
                recoilStrength = 0.01;
                break;
            case 'shotgun':
                recoilStrength = 0.05;
                break;
            case 'assault':
                recoilStrength = 0.02;
                break;
            case 'sniper':
                recoilStrength = 0.08;
                break;
        }

        this.mouseY -= recoilStrength;
        this.mouseY = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.mouseY));
    }

    // 重新裝彈
    reload() {
        const weapon = this.currentWeapon;
        if (weapon.currentAmmo < weapon.maxAmmo) {
            weapon.currentAmmo = weapon.maxAmmo;
            console.log(`🔄 ${weapon.name} 重新裝彈完成`);
            this.showReloadNotification();
        }
    }

    showReloadNotification() {
        // 創建重新裝彈通知
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 40%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 255, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            pointer-events: none;
        `;
        notification.textContent = '🔄 重新裝彈完成';

        document.body.appendChild(notification);

        // 0.8秒後移除通知
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 800);
    }

    playShootSound(weaponType) {
        // 創建音效反饋（視覺和聲音）
        this.createMuzzleFlash();

        switch (weaponType) {
            case 'pistol':
                console.log('🔫 Bang!');
                break;
            case 'shotgun':
                console.log('💥 BOOM!');
                break;
            case 'assault':
                console.log('🔥 Rat-tat-tat!');
                break;
            case 'sniper':
                console.log('⚡ CRACK!');
                break;
        }
    }

    createMuzzleFlash() {
        // 創建槍口閃光效果
        const flashGeometry = new THREE.SphereGeometry(0.3, 6, 6);
        const flashMaterial = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.8
        });
        const flash = new THREE.Mesh(flashGeometry, flashMaterial);

        // 在武器前方創建閃光
        flash.position.copy(this.camera.position);
        flash.position.add(this.player.direction.clone().multiplyScalar(1.5));

        this.scene.add(flash);

        // 0.1秒後移除閃光
        setTimeout(() => {
            this.scene.remove(flash);
        }, 100);
    }

    showLowAmmoWarning() {
        // 創建彈藥不足警告
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 30%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 165, 0, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            pointer-events: none;
        `;
        warning.textContent = '⚠️ 彈藥不足！';

        document.body.appendChild(warning);

        // 1秒後移除警告
        setTimeout(() => {
            if (document.body.contains(warning)) {
                document.body.removeChild(warning);
            }
        }, 1000);
    }

    // 更新子彈
    updateBullets(deltaTime) {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            // 移動子彈
            const movement = bullet.direction.clone().multiplyScalar(bullet.speed * deltaTime);
            bullet.mesh.position.add(movement);

            // 減少生命值
            bullet.life -= deltaTime;

            // 減少調試輸出頻率
            if (Math.floor(bullet.life * 2) % 4 === 0) {
                // console.log(`子彈 ${i} 位置: (${bullet.mesh.position.x.toFixed(1)}, ${bullet.mesh.position.y.toFixed(1)}, ${bullet.mesh.position.z.toFixed(1)})`);
            }

            // 檢查是否需要移除子彈（更大的邊界）
            if (bullet.life <= 0 ||
                Math.abs(bullet.mesh.position.x) > 350 ||
                Math.abs(bullet.mesh.position.z) > 350 ||
                bullet.mesh.position.y < -10) { // 允許子彈稍微低於地面

                this.scene.remove(bullet.mesh);
                this.bullets.splice(i, 1);
                console.log(`移除子彈 ${i}`);
            }
        }
    }

    // 更新敵人
    updateEnemies(deltaTime) {
        // 隨機生成敵人（平衡的喪屍數量）
        if (Math.random() < 0.015 && this.enemies.length < 8) {
            this.spawnEnemy();
        }

        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];

            // 只有非測試目標才會移動
            if (enemy.type !== 'test') {
                // 計算到玩家的距離
                const distanceToPlayer = enemy.position.distanceTo(this.player.position);

                // 檢查是否在檢測範圍內
                if (distanceToPlayer <= enemy.detectionRange) {
                    if (!enemy.isChasing) {
                        enemy.isChasing = true;
                        console.log(`🧟 喪屍發現玩家！距離: ${distanceToPlayer.toFixed(1)}m`);
                    }

                    // 朝玩家移動
                    const direction = new THREE.Vector3()
                        .subVectors(this.player.position, enemy.position)
                        .normalize();

                    enemy.position.add(direction.multiplyScalar(enemy.speed * deltaTime));
                } else {
                    // 超出檢測範圍，停止追逐
                    if (enemy.isChasing) {
                        enemy.isChasing = false;
                        console.log(`🧟 喪屍失去目標`);
                    }

                    // 隨機遊蕩
                    if (Math.random() < 0.01) { // 1% 機率改變方向
                        enemy.wanderDirection = new THREE.Vector3(
                            (Math.random() - 0.5) * 2,
                            0,
                            (Math.random() - 0.5) * 2
                        ).normalize();
                    }

                    if (enemy.wanderDirection) {
                        enemy.position.add(enemy.wanderDirection.clone().multiplyScalar(enemy.speed * 0.3 * deltaTime));
                    }
                }

                // 確保喪屍在適當高度
                enemy.position.y = 1;
                enemy.mesh.position.copy(enemy.position);

                // 更新喪屍狀態指示器
                this.updateZombieIndicator(enemy);
            }

            // 檢查敵人是否在攻擊範圍內（只計算水平距離）
            const horizontalDistance = Math.sqrt(
                Math.pow(enemy.position.x - this.player.position.x, 2) +
                Math.pow(enemy.position.z - this.player.position.z, 2)
            );

            if (horizontalDistance < enemy.attackRange && enemy.isChasing) { // 使用個別攻擊距離，且必須在追逐狀態
                const currentTime = Date.now();
                if (currentTime - enemy.lastAttackTime > 1000) { // 1秒攻擊間隔
                    this.takeDamage(enemy.damage);
                    enemy.lastAttackTime = currentTime;
                    console.log(`喪屍攻擊！造成 ${enemy.damage} 點傷害，距離: ${horizontalDistance.toFixed(1)}m`);

                    // 添加攻擊視覺效果
                    this.createDamageEffect();
                }
            }
        }
    }

    // 生成喪屍
    spawnEnemy() {
        const zombieGroup = this.createZombieModel();

        // 隨機位置（遠離玩家，更大範圍）
        let position;
        do {
            position = new THREE.Vector3(
                (Math.random() - 0.5) * 550,
                1, // 設置喪屍初始高度
                (Math.random() - 0.5) * 550
            );
        } while (position.distanceTo(this.player.position) < 30); // 減少最小距離

        zombieGroup.position.copy(position);
        this.scene.add(zombieGroup);

        // 隨機喪屍類型
        const zombieTypes = ['normal', 'fast', 'tank'];
        const type = zombieTypes[Math.floor(Math.random() * zombieTypes.length)];

        let health, speed, damage;
        let detectionRange, attackRange;

        switch (type) {
            case 'fast':
                health = 20;
                speed = 25;
                damage = 15;
                detectionRange = 35; // 快速喪屍檢測範圍更大
                attackRange = 2.5; // 攻擊距離較小
                zombieGroup.scale.set(0.6, 0.6, 0.6);
                break;
            case 'tank':
                health = 80;
                speed = 8;
                damage = 30;
                detectionRange = 25; // 坦克喪屍檢測範圍較小
                attackRange = 4; // 攻擊距離較大
                zombieGroup.scale.set(1.0, 1.0, 1.0);
                break;
            default: // normal
                health = 40;
                speed = 15;
                damage = 20;
                detectionRange = 30; // 普通檢測範圍
                attackRange = 3; // 普通攻擊距離
                zombieGroup.scale.set(0.8, 0.8, 0.8);
                break;
        }

        const enemy = {
            mesh: zombieGroup,
            position: position,
            health: health,
            maxHealth: health,
            speed: speed,
            damage: damage,
            type: type,
            lastAttackTime: 0,
            detectionRange: detectionRange, // 檢測玩家的距離
            attackRange: attackRange, // 攻擊距離
            isChasing: false // 是否正在追逐玩家
        };

        this.enemies.push(enemy);
    }

    updateZombieIndicator(enemy) {
        // 為追逐中的喪屍添加紅色指示器
        if (enemy.isChasing && !enemy.indicator) {
            const indicatorGeometry = new THREE.SphereGeometry(0.2, 8, 8);
            const indicatorMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.8
            });
            enemy.indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
            enemy.mesh.add(enemy.indicator);
            enemy.indicator.position.set(0, 4, 0); // 在喪屍頭頂
        } else if (!enemy.isChasing && enemy.indicator) {
            // 移除指示器
            enemy.mesh.remove(enemy.indicator);
            enemy.indicator = null;
        }

        // 讓指示器閃爍
        if (enemy.indicator) {
            enemy.indicator.material.opacity = 0.5 + 0.3 * Math.sin(Date.now() * 0.01);
        }

        // 更新血量條
        this.updateEnemyHealthBar(enemy);
    }

    updateEnemyHealthBar(enemy) {
        // 只為受傷的敵人顯示血量條
        if (enemy.health < enemy.maxHealth) {
            if (!enemy.healthBar) {
                // 創建血量條
                const healthBarGroup = new THREE.Group();

                // 背景條
                const bgGeometry = new THREE.PlaneGeometry(2, 0.2);
                const bgMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
                const bgBar = new THREE.Mesh(bgGeometry, bgMaterial);
                healthBarGroup.add(bgBar);

                // 血量條
                const healthGeometry = new THREE.PlaneGeometry(2, 0.15);
                const healthMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
                const healthBar = new THREE.Mesh(healthGeometry, healthMaterial);
                healthBar.position.z = 0.01;
                healthBarGroup.add(healthBar);

                enemy.healthBar = healthBarGroup;
                enemy.healthBarFill = healthBar;
                enemy.mesh.add(healthBarGroup);
                healthBarGroup.position.set(0, 5, 0);

                // 讓血量條始終面向相機
                healthBarGroup.lookAt(this.camera.position);
            }

            // 更新血量條寬度
            const healthPercent = enemy.health / enemy.maxHealth;
            enemy.healthBarFill.scale.x = healthPercent;
            enemy.healthBarFill.position.x = -(1 - healthPercent);

            // 根據血量改變顏色
            if (healthPercent > 0.6) {
                enemy.healthBarFill.material.color.setHex(0x00ff00);
            } else if (healthPercent > 0.3) {
                enemy.healthBarFill.material.color.setHex(0xffff00);
            } else {
                enemy.healthBarFill.material.color.setHex(0xff0000);
            }
        }
    }

    createZombieModel() {
        const zombieGroup = new THREE.Group();

        // 腿部（縮小尺寸）
        const legGeometry = new THREE.BoxGeometry(0.3, 1.2, 0.3);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x3a3a3a });

        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.25, 0.6, 0); // 縮小腿部
        zombieGroup.add(leftLeg);

        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.25, 0.6, 0);
        zombieGroup.add(rightLeg);

        // 身體（縮小）
        const bodyGeometry = new THREE.BoxGeometry(1, 1.8, 0.6);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a4a4a });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 2.1; // 調整身體位置
        zombieGroup.add(body);

        // 頭部（縮小）
        const headGeometry = new THREE.BoxGeometry(0.7, 0.7, 0.7);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0x6b5b73 });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 3.35; // 調整頭部位置
        zombieGroup.add(head);

        // 眼睛（簡化為方塊）
        const eyeGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
        const eyeMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.2, 3.4, 0.3);
        zombieGroup.add(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.2, 3.4, 0.3);
        zombieGroup.add(rightEye);

        // 手臂（縮小）
        const armGeometry = new THREE.BoxGeometry(0.25, 1, 0.25);
        const armMaterial = new THREE.MeshLambertMaterial({ color: 0x4a4a4a });

        const leftArm = new THREE.Mesh(armGeometry, armMaterial);
        leftArm.position.set(-0.7, 2.2, 0); // 調整手臂位置
        leftArm.rotation.z = 0.3;
        zombieGroup.add(leftArm);

        const rightArm = new THREE.Mesh(armGeometry, armMaterial);
        rightArm.position.set(0.7, 2.2, 0);
        rightArm.rotation.z = -0.3;
        zombieGroup.add(rightArm);

        // 設置陰影
        zombieGroup.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });

        return zombieGroup;
    }

    // 生成測試目標（按T鍵）
    spawnTestTarget() {
        const testGeometry = new THREE.BoxGeometry(3, 3, 3);
        const testMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        const testMesh = new THREE.Mesh(testGeometry, testMaterial);

        // 在玩家前方10米處生成
        const targetPosition = this.player.position.clone();
        targetPosition.add(this.player.direction.clone().multiplyScalar(10));
        targetPosition.y = 2; // 設置適當高度

        testMesh.position.copy(targetPosition);
        this.scene.add(testMesh);

        // 添加到敵人列表中進行碰撞檢測
        const testEnemy = {
            mesh: testMesh,
            position: targetPosition,
            health: 50,
            maxHealth: 50,
            speed: 0, // 靜止目標
            damage: 0,
            type: 'test',
            lastAttackTime: 0
        };

        this.enemies.push(testEnemy);
        console.log(`🎯 生成測試目標，位置: (${targetPosition.x.toFixed(1)}, ${targetPosition.y.toFixed(1)}, ${targetPosition.z.toFixed(1)})`);
    }

    // 碰撞檢測
    checkCollisions() {
        // 子彈與敵人的碰撞
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];

                // 使用敵人網格的實際位置而不是邏輯位置
                const enemyPos = enemy.mesh.position;
                const bulletPos = bullet.mesh.position;

                // 計算3D距離
                const distance = bulletPos.distanceTo(enemyPos);

                // 減少調試信息輸出
                if (distance < 6 && Math.random() < 0.1) { // 只有10%機率輸出調試信息
                    // console.log(`子彈位置: (${bulletPos.x.toFixed(1)}, ${bulletPos.y.toFixed(1)}, ${bulletPos.z.toFixed(1)})`);
                    // console.log(`喪屍位置: (${enemyPos.x.toFixed(1)}, ${enemyPos.y.toFixed(1)}, ${enemyPos.z.toFixed(1)})`);
                    // console.log(`距離: ${distance.toFixed(2)}`);
                }

                if (distance < 5) { // 大幅增加碰撞範圍
                    // 命中敵人
                    enemy.health -= bullet.damage;
                    console.log(`🎯 命中！造成 ${bullet.damage} 傷害，敵人剩餘血量: ${enemy.health}`);

                    // 創建命中效果和傷害數字
                    this.createHitEffect(enemyPos);
                    this.showDamageNumber(enemyPos, bullet.damage);

                    // 移除子彈
                    this.scene.remove(bullet.mesh);
                    this.bullets.splice(i, 1);

                    // 檢查敵人是否死亡
                    if (enemy.health <= 0) {
                        this.scene.remove(enemy.mesh);
                        this.enemies.splice(j, 1);

                        // 根據敵人類型給予不同分數
                        const points = enemy.type === 'tank' ? 200 : enemy.type === 'fast' ? 150 : 100;
                        this.score += points;
                        console.log(`💀 擊殺 ${enemy.type} 喪屍！獲得 ${points} 分`);

                        // 隨機掉落資源
                        this.dropResources(enemyPos);

                        // 創建爆炸效果
                        this.createExplosion(enemyPos);

                        // 檢查武器解鎖
                        this.checkWeaponUnlocks();
                    }

                    break;
                }
            }
        }
    }

    // 資源掉落系統
    dropResources(position) {
        const dropChance = Math.random();

        if (dropChance < 0.3) { // 30% 機率掉落彈藥
            this.createAmmoPickup(position);
        } else if (dropChance < 0.4) { // 10% 機率掉落醫療包
            this.createHealthPickup(position);
        } else if (dropChance < 0.45) { // 5% 機率掉落廢料
            this.createScrapPickup(position);
        }
    }

    createAmmoPickup(position) {
        const ammoGroup = new THREE.Group();

        // 主體彈藥盒（更大）
        const ammoGeometry = new THREE.BoxGeometry(2, 1, 2);
        const ammoMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
        const ammoMesh = new THREE.Mesh(ammoGeometry, ammoMaterial);
        ammoMesh.position.y = 0.5;
        ammoGroup.add(ammoMesh);

        // 添加發光效果
        const glowGeometry = new THREE.BoxGeometry(2.5, 1.5, 2.5);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0xFFD700,
            transparent: true,
            opacity: 0.3
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.position.y = 0.5;
        ammoGroup.add(glowMesh);

        ammoGroup.position.copy(position);
        ammoGroup.position.y = 2; // 提高位置
        this.scene.add(ammoGroup);

        // 添加到可拾取物品列表
        if (!this.pickups) this.pickups = [];
        this.pickups.push({
            mesh: ammoGroup,
            type: 'ammo',
            position: ammoGroup.position
        });
    }

    createHealthPickup(position) {
        const healthGroup = new THREE.Group();

        // 主體醫療包（十字形狀）
        const healthGeometry = new THREE.BoxGeometry(1.5, 1.5, 1.5);
        const healthMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 });
        const healthMesh = new THREE.Mesh(healthGeometry, healthMaterial);
        healthMesh.position.y = 0.75;
        healthGroup.add(healthMesh);

        // 十字標記
        const crossGeometry1 = new THREE.BoxGeometry(0.3, 1.8, 0.3);
        const crossGeometry2 = new THREE.BoxGeometry(1.8, 0.3, 0.3);
        const crossMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFFFF });

        const cross1 = new THREE.Mesh(crossGeometry1, crossMaterial);
        const cross2 = new THREE.Mesh(crossGeometry2, crossMaterial);
        cross1.position.set(0, 0.75, 0.76);
        cross2.position.set(0, 0.75, 0.76);
        healthGroup.add(cross1);
        healthGroup.add(cross2);

        // 添加發光效果
        const glowGeometry = new THREE.BoxGeometry(2, 2, 2);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0xFF0000,
            transparent: true,
            opacity: 0.3
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.position.y = 0.75;
        healthGroup.add(glowMesh);

        healthGroup.position.copy(position);
        healthGroup.position.y = 2;
        this.scene.add(healthGroup);

        if (!this.pickups) this.pickups = [];
        this.pickups.push({
            mesh: healthGroup,
            type: 'health',
            position: healthGroup.position
        });
    }

    createScrapPickup(position) {
        const scrapGroup = new THREE.Group();

        // 主體廢料（更大的八面體）
        const scrapGeometry = new THREE.OctahedronGeometry(1.2);
        const scrapMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
        const scrapMesh = new THREE.Mesh(scrapGeometry, scrapMaterial);
        scrapMesh.position.y = 1.2;
        scrapGroup.add(scrapMesh);

        // 添加一些小碎片
        for (let i = 0; i < 3; i++) {
            const fragmentGeometry = new THREE.DodecahedronGeometry(0.3);
            const fragmentMaterial = new THREE.MeshLambertMaterial({ color: 0x808080 });
            const fragment = new THREE.Mesh(fragmentGeometry, fragmentMaterial);
            fragment.position.set(
                (Math.random() - 0.5) * 2,
                0.3 + Math.random() * 0.5,
                (Math.random() - 0.5) * 2
            );
            scrapGroup.add(fragment);
        }

        // 添加發光效果
        const glowGeometry = new THREE.OctahedronGeometry(1.5);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0xC0C0C0,
            transparent: true,
            opacity: 0.2
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.position.y = 1.2;
        scrapGroup.add(glowMesh);

        scrapGroup.position.copy(position);
        scrapGroup.position.y = 2;
        this.scene.add(scrapGroup);

        if (!this.pickups) this.pickups = [];
        this.pickups.push({
            mesh: scrapGroup,
            type: 'scrap',
            position: scrapGroup.position
        });
    }

    // 創建命中效果（優化版）
    createHitEffect(position) {
        const hitParticleCount = 3; // 減少粒子數量

        for (let i = 0; i < hitParticleCount; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.15, 0.15, 0.15); // 使用方塊
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            particle.position.copy(position);
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 1,
                Math.random() * 2,
                (Math.random() - 0.5) * 1
            ));

            this.scene.add(particle);

            this.particles.push({
                mesh: particle,
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 5,
                    Math.random() * 3,
                    (Math.random() - 0.5) * 5
                ),
                life: 0.3 // 更短的生命週期
            });
        }
    }

    // 創建爆炸效果（優化版）
    createExplosion(position) {
        const particleCount = 6; // 減少粒子數量

        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3); // 使用方塊代替球體
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(Math.random() * 0.1, 1, 0.5)
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            particle.position.copy(position);
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ));

            this.scene.add(particle);

            this.particles.push({
                mesh: particle,
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 20,
                    Math.random() * 10,
                    (Math.random() - 0.5) * 20
                ),
                life: 0.6 // 減少生命週期
            });
        }
    }

    // 更新粒子效果（優化版）
    updateParticles(deltaTime) {
        // 限制粒子總數
        if (this.particles.length > 50) {
            // 移除最老的粒子
            const oldParticle = this.particles.shift();
            this.scene.remove(oldParticle.mesh);
        }

        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            // 移動粒子
            particle.mesh.position.add(
                particle.velocity.clone().multiplyScalar(deltaTime)
            );

            // 重力
            particle.velocity.y -= 9.8 * deltaTime;

            // 減少生命值
            particle.life -= deltaTime;

            // 淡出效果
            particle.mesh.material.opacity = Math.max(0, particle.life);
            particle.mesh.material.transparent = true;

            // 移除過期粒子
            if (particle.life <= 0) {
                this.scene.remove(particle.mesh);
                this.particles.splice(i, 1);
            }
        }
    }

    // 更新拾取物品
    updatePickups(deltaTime) {
        if (!this.pickups) return;

        for (let i = 0; i < this.pickups.length; i++) {
            const pickup = this.pickups[i];
            // 讓拾取物品旋轉
            pickup.mesh.rotation.y += deltaTime * 2;

            // 上下浮動動畫（更明顯）
            const baseY = pickup.type === 'ammoBox' ? 2 : 2;
            pickup.mesh.position.y = baseY + Math.sin(Date.now() * 0.005 + i) * 0.5;

            // 添加脈衝效果
            const pulseScale = 1 + Math.sin(Date.now() * 0.008 + i) * 0.1;
            pickup.mesh.scale.set(pulseScale, pulseScale, pulseScale);
        }
    }

    // 檢查拾取物品碰撞
    checkPickupCollisions() {
        if (!this.pickups) return;

        for (let i = this.pickups.length - 1; i >= 0; i--) {
            const pickup = this.pickups[i];
            const distance = pickup.position.distanceTo(this.player.position);

            if (distance < 8) { // 增加拾取距離
                const shouldRemove = this.collectPickup(pickup);
                if (shouldRemove) {
                    this.scene.remove(pickup.mesh);
                    this.pickups.splice(i, 1);
                }
            }
        }
    }

    collectPickup(pickup) {
        let message = '';
        let color = '#00ff00';

        switch (pickup.type) {
            case 'ammo':
                const ammoGained = Math.floor(this.currentWeapon.maxAmmo * 0.5);
                this.currentWeapon.currentAmmo = Math.min(
                    this.currentWeapon.currentAmmo + ammoGained,
                    this.currentWeapon.maxAmmo
                );
                message = `${this.currentWeapon.icon} +${ammoGained} 彈藥`;
                color = '#ffff00';
                console.log('🔫 拾取彈藥！');
                break;
            case 'ammoBox':
                // 彈藥箱可以多次使用
                this.currentWeapon.currentAmmo = this.currentWeapon.maxAmmo;
                pickup.uses--;
                message = `${this.currentWeapon.icon} 彈藥補滿`;
                color = '#ffff00';
                console.log(`📦 使用彈藥箱！剩餘使用次數: ${pickup.uses}`);

                // 如果還有使用次數，不移除物品
                if (pickup.uses > 0) {
                    this.showPickupMessage(message, color);
                    return false; // 不移除
                }
                break;
            case 'health':
                const healthGained = Math.min(30, 100 - this.health);
                this.health = Math.min(this.health + 30, 100);
                message = `+${healthGained} 生命值`;
                color = '#ff4444';
                console.log('❤️ 拾取醫療包！');
                break;
            case 'scrap':
                this.resources.scrap += 1;
                message = '+1 廢料';
                color = '#888888';
                console.log('🔧 拾取廢料！');
                break;
        }

        this.showPickupMessage(message, color);
        return true; // 移除物品
    }

    showPickupMessage(message, color) {
        // 創建拾取消息
        const pickupElement = document.createElement('div');
        pickupElement.style.cssText = `
            position: fixed;
            top: 25%;
            left: 50%;
            transform: translateX(-50%);
            color: ${color};
            font-size: 20px;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        `;
        pickupElement.textContent = message;

        document.body.appendChild(pickupElement);

        // 動畫效果
        let opacity = 1;
        let yOffset = 0;
        const animate = () => {
            opacity -= 0.015;
            yOffset -= 1;

            pickupElement.style.opacity = opacity;
            pickupElement.style.top = `${25 + yOffset * 0.1}%`;

            if (opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                if (document.body.contains(pickupElement)) {
                    document.body.removeChild(pickupElement);
                }
            }
        };

        requestAnimationFrame(animate);
    }

    // 受到傷害
    takeDamage(damage) {
        this.health -= damage;
        console.log(`受到 ${damage} 點傷害！剩餘生命值: ${this.health}`);

        if (this.health <= 0) {
            this.gameOver();
        }
    }

    // 創建跳躍效果
    createJumpEffect() {
        // 創建地面塵土效果
        const dustParticleCount = 8;

        for (let i = 0; i < dustParticleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.1, 4, 4);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: 0x8B7355, // 土色
                transparent: true,
                opacity: 0.6
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            // 在玩家腳下生成粒子
            particle.position.copy(this.player.position);
            particle.position.y = 2; // 地面高度
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                0,
                (Math.random() - 0.5) * 2
            ));

            this.scene.add(particle);

            this.particles.push({
                mesh: particle,
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 8,
                    Math.random() * 5,
                    (Math.random() - 0.5) * 8
                ),
                life: 0.8 // 短暫的塵土效果
            });
        }
    }

    // 創建著陸效果
    createLandingEffect() {
        // 創建著陸塵土效果
        const dustParticleCount = 6;

        for (let i = 0; i < dustParticleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.15, 4, 4);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: 0x8B7355, // 土色
                transparent: true,
                opacity: 0.5
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            // 在玩家腳下生成粒子
            particle.position.copy(this.player.position);
            particle.position.y = 2; // 地面高度
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 1.5,
                0,
                (Math.random() - 0.5) * 1.5
            ));

            this.scene.add(particle);

            this.particles.push({
                mesh: particle,
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 6,
                    Math.random() * 3,
                    (Math.random() - 0.5) * 6
                ),
                life: 0.6 // 短暫的著陸效果
            });
        }
    }

    // 創建受傷視覺效果
    createDamageEffect() {
        // 創建紅色閃爍效果
        const damageOverlay = document.createElement('div');
        damageOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 0, 0, 0.3);
            pointer-events: none;
            z-index: 50;
        `;
        document.body.appendChild(damageOverlay);

        // 0.2秒後移除效果
        setTimeout(() => {
            document.body.removeChild(damageOverlay);
        }, 200);
    }

    // 遊戲結束
    gameOver() {
        this.gameState = 'gameOver';
        document.getElementById('finalScore').textContent = `最終分數: ${this.score}`;
        document.getElementById('gameOver').style.display = 'flex';

        // 釋放指針鎖定
        document.exitPointerLock();
    }
}

// 當頁面載入完成時啟動遊戲
window.addEventListener('load', () => {
    try {
        console.log('🎮 開始載入遊戲...');

        // 檢查Three.js是否載入
        if (typeof THREE === 'undefined') {
            throw new Error('Three.js 庫未載入');
        }

        console.log('✅ Three.js 載入成功');

        // 創建遊戲實例
        const game = new FPSGame();
        console.log('✅ 遊戲初始化完成');

        // 隱藏載入畫面
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }

    } catch (error) {
        console.error('❌ 遊戲載入失敗:', error);

        // 顯示錯誤信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            text-align: center;
        `;
        errorDiv.innerHTML = `
            <h3>遊戲載入失敗</h3>
            <p>${error.message}</p>
            <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px;">重新載入</button>
        `;
        document.body.appendChild(errorDiv);

        // 隱藏載入畫面
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }
    }
});
