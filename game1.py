import pygame
import random 
import os
FPS = 90 

black = (0, 0, 0)
HEIGHT = 1080  
WIDTH = 1920
WHITE = (255, 255, 255)
GREEN = (0, 255, 0  ) 
RED = (255, 0, 0)
blue  = (0, 225, 0)
pygame.init()
pygame.mixer.init()
screen = pygame.display.set_mode((WIDTH, HEIGHT)) 
pygame.display.set_caption("糞game")
clock = pygame.time.Clock()
#載入圖片
blackground_img = pygame.image.load(os.path.join("img", "blackground.png")).convert()
player_img = pygame.image.load(os.path.join("img", "player.png")).convert()
player_mini_img = pygame.transform.scale(player_img, (50, 38))
player_mini_img.set_colorkey(black)
pygame.display.set_icon(player_mini_img)
rock_img = pygame.image.load(os.path.join("img", "rock.png")).convert()
bullet_img = pygame.image.load(os.path.join("img", "bullet.png")).convert()
rock_imgs = []
for i in range(7):
    rock_imgs.append(pygame.image.load(os.path.join("img", f"rock{i}.png")).convert())
explosion3_sound = pygame.mixer.Sound(os.path.join("sound", "explosion3.mp3"))

shoot_sound = pygame.mixer.Sound(os.path.join("sound", "shoot.wav"))
gun_sound = pygame.mixer.Sound(os.path.join("sound", "pow1.wav"))
shield_sound = pygame.mixer.Sound(os.path.join("sound", "pow0.wav"))
die_sound = pygame.mixer.Sound(os.path.join("sound", "rumble.ogg"))
shoot_sound.set_volume(0.2)
expl_sounds = [
    pygame.mixer.Sound(os.path.join("sound", "expl0.wav")),
    pygame.mixer.Sound(os.path.join("sound", "expl1.wav")),
    pygame.mixer.Sound(os.path.join("sound", "explosion3.mp3"))
]

expl_anim = {}
expl_anim['lg'] = []
expl_anim['sm'] = []
expl_anim['player'] = []
for i in range(9):
    expl_img = pygame.image.load(os.path.join("img", f"expl{i}.png")).convert()
    expl_img.set_colorkey(black)
    expl_anim['lg'].append(pygame.transform.scale(expl_img, (125, 125)))
    expl_anim['sm'].append(pygame.transform.scale(expl_img, (50, 50)))
    player_expl_img = pygame.image.load(os.path.join("img", f"player_expl{i}.png")).convert()
    player_expl_img.set_colorkey(black)
    expl_anim['player'].append(player_expl_img)
    expl_anim['lg'].append(player_expl_img)

power_imgs = {}
power_imgs["shield"] = pygame.image.load(os.path.join("img", "shield.png")).convert()
power_imgs["gun"] = pygame.image.load(os.path.join("img", "gun.png")).convert()

pygame.mixer.music.load(os.path.join("sound", "background.ogg"))
for sound in expl_sounds:
    sound.set_volume(2.7)

font_name = os.path.join("font.ttf")

def draw_text(surf, text, size, x, y):
    font = pygame.font.Font(font_name, size) 
    text_surface = font.render(text, True, WHITE) 
    text_rect = text_surface.get_rect()
    text_rect.centerx = x
    text_rect.centery = y
    surf.blit(text_surface, text_rect)
    
def new_rock():
    r = rock(hit.num)
    all_sprites.add(r) 
    rocks.add(r)

def draw_health(surf, hp, x, y ):
    if hp < 0:
        hp = 0
    BAR_LENGTH = 250
    BAR_HEIGHT = 25
    fill = (hp / 100)*BAR_LENGTH
    outline_rect = pygame.Rect(x, y, BAR_LENGTH, BAR_HEIGHT)
    fill_rect = pygame.Rect(x, y, fill, BAR_HEIGHT)
    pygame.draw.rect(surf, GREEN, fill_rect)
    pygame.draw.rect(surf, WHITE, outline_rect, 2)

def draw_lives(surf, lives, img, x, y):
    for i in range(lives):
        img_rect = img.get_rect()
        img_rect.x = x + 30 * i
        img_rect.y = y
        surf.blit(img, img_rect)


def draw_init():
    screen.blit(blackground_img, (0, 0))
    draw_text(screen, "糞game", 64, WIDTH/2, HEIGHT/4)
    draw_text(screen, '← → or A D 移動飛船 空白鍵發射子彈~', 22, WIDTH/2, HEIGHT/2)
    draw_text(screen, '按任意鍵開始遊戲!', 18, WIDTH/2, HEIGHT*3/4)
    pygame.display.update()
    waiting = True
    while waiting:
        clock.tick(FPS)
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                return  True
            elif event.type == pygame.KEYUP:
                waiting = False
                return  False

class Player(pygame.sprite.Sprite):
    def __init__(self):
        pygame.sprite.Sprite.__init__(self) 
        self.image = pygame.transform.scale(player_img, (60, 60 ))
        self.image.set_colorkey(black)
        self.rect = self.image.get_rect()
        self.radious = 5
        # pygame.draw.circle(self.image, RED,self.rect.center, self.radious)
        self.rect = self.image.get_rect()
        self.rect.centerx = WIDTH / 2
        self.rect.bottom = HEIGHT - 10
        self.speedx = 10 # 速度 
        self.health = 100
        self.live = 3
        self.hidden = False
        self.hide_timer = 0
        self.shoot_delay = 100  # 射擊延遲時間（毫秒）
        self.last_shot = pygame.time.get_ticks()
        self.gun = 1
        self.gun_time = 1
        

    def update(self):
        now = pygame.time.get_ticks()
        if self.gun > 1 and (now - self.gun_time > 5000):
            self.gun -= 1
            self.gun_time = now

        if self.hidden and (now - self.hide_timer > 1000):
            self.hidden = False
            self.rect.centerx = WIDTH / 2
            self.rect.bottom = HEIGHT - 10
        player_x = 400
        player_y = 300
        player_speed = 5
        keys = pygame.key.get_pressed()
        key_pressed = pygame.key.get_pressed()
        if key_pressed[pygame.K_d]:
            self.rect.x += self.speedx
        if key_pressed[pygame.K_a]:
            self.rect.x -= self.speedx
        if keys[pygame.K_LEFT]:
            player_x -= player_speed
        if keys[pygame.K_RIGHT]:
            player_x += player_speed
        if keys[pygame.K_UP]:
            player_y -= player_speed
        if keys[pygame.K_DOWN]:
            player_y += player_speed
        if key_pressed[pygame.K_RIGHT]:
            self.rect.x += self.speedx
        if key_pressed[pygame.K_LEFT]:
            self.rect.x -= self.speedx
        if self.rect.right > WIDTH:
            self.rect.right = WIDTH
        if self.rect.left < 0:
            self.rect.left = 0
    def shoot(self): 
        if not(self.hidden):
            now = pygame.time.get_ticks()
            if now - self.last_shot > self.shoot_delay:
                self.last_shot = now
                if self.gun == 1:
                    bullet = Bullet(self.rect.centerx, self.rect.top)
                    all_sprites.add(bullet)
                    bullets.add(bullet)
                    shoot_sound.play()
                elif self.gun >=2:
                    bullet1 = Bullet(self.rect.left, self.rect.centery)
                    bullet2 = Bullet(self.rect.right, self.rect.centery)
                    all_sprites.add(bullet1)
                    all_sprites.add(bullet2)
                    bullets.add(bullet1)
                    bullets.add(bullet2)
                    shoot_sound.play()
    def hide(self):
        self.hidden = True
        self.hide_time = pygame.time.get_ticks()
        self.rect.center = (WIDTH/2, HEIGHT+500)

    def gunup(self):
        self.gun += 1
        self.guntime = pygame.time.get_ticks()

class rock(pygame.sprite.Sprite):
    def __init__(self, num): # 加入參數 num 來接收數字
        pygame.sprite.Sprite.__init__(self) 
        self.image_ori =  random.choice(rock_imgs)
        self.image_ori.set_colorkey(black)
        self.image = self.image_ori.copy()
        self.rect = self.image.get_rect()
        self.radious = int (self.rect.width *1.5 / 2)
        # self.radious = self.rect.height / 2
        # pygame.draw.circle(self.image, RED, self.rect.center, self.radious)
        self.image.set_colorkey(black)
        self.rect.x =random.randrange(0, WIDTH - self.rect.width)
        self.rect.y =random.randrange(-900, -400)
        self.speedy = random.randrange(5, 9)
        self.speedx = random.randrange(-4, 4)
        self.num = num # 將數字儲存在物件屬性中
        self.rot_degee = random.randrange(-5,5)
        self.total_degree = 0
            
    def rotate(self):
        self.total_degree += self.rot_degee
        self.total_degree = self.total_degree % 360
        self.image = pygame.transform.rotate(self.image_ori, self.total_degree)
        center = self.rect.center
        self.rect = self.image.get_rect()
        self.rect.center = center

    def update(self):
       self.rotate()
       self.rect.y += self.speedy
       self.rect.x += self.speedx
       if self.rect.top > HEIGHT or self.rect.left > WIDTH or self.rect.right <0:   
            self.rect.x =random.randrange(0, WIDTH - self.rect.width)
            self.rect.y =random.randrange(-5, -2)
            self.speedy = random.randrange(8, 15)
            self.speedx = random.randrange(-2, 2)
    #    draw_text(self.image, str(self.num), 20, self.rect.width / 2, self.rect.height / 2) # 在方塊上繪製數字

class Bullet(pygame.sprite.Sprite):
    def __init__(self,  x, y):
        pygame.sprite.Sprite.__init__(self) 
        self.image = bullet_img
        self.image.set_colorkey(black)
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.bottom = y
        self.speedy = -20

    def update(self):
       self.rect.y += self.speedy
       if self.rect.bottom < 0:
           self.kill()

class Explosion(pygame.sprite.Sprite):
    def __init__(self, center, size):
        pygame.sprite.Sprite.__init__(self)
        self.size = size
        self.image = expl_anim[self.size][0]
        self.rect = self.image.get_rect()
        self.rect.center = center
        self.frame = 0
        self.last_update = pygame.time.get_ticks()
        self.frame_rate = 50

    def update(self):
        now = pygame.time.get_ticks()
        if now - self.last_update > self.frame_rate:
            self.last_update = now
            self.frame += 1
            if self.frame == len(expl_anim[self.size]):
                self.kill()
            else:
                self.image = expl_anim[self.size][self.frame]
                center = self.rect.center
                self.rect = self.image.get_rect()
                self.rect.center = center

class Power(pygame.sprite.Sprite):
    def __init__(self, center):
        pygame.sprite.Sprite.__init__(self) 
        self.type = random.choice(['shield', 'gun'])
        self.image = power_imgs[self.type]
        self.image.set_colorkey(black)
        self.rect = self.image.get_rect()
        self.rect.center = center
        self.speedy = 3

    def update(self):
       self.rect.y += self.speedy
       if self.rect.top > HEIGHT:
           self.kill()

def update(self):
    self.rect.y += self.speedy
    if self.rect.bottom < 0:
        self.kill()

# all_sprites = pygame.sprite.Group()
# rocks = pygame.sprite.Group()
# bullets = pygame.sprite.Group()
# powers= pygame.sprite.Group()

# player = Player()
# all_sprites.add(player)
# for i in range(10):
#     r = rock(i+1) # 建立 rock 物件時傳入數字
#     all_sprites.add(r)
#     rocks.add(r)
#遊戲迴圈  
score = 0
pygame.mixer.music.play(-1)   

show_init = True
running = True  
while running:
    if show_init:
        close = draw_init()
        if close:
            break
        show_init = False      
        all_sprites = pygame.sprite.Group()
        rocks = pygame.sprite.Group()
        bullets = pygame.sprite.Group()
        powers= pygame.sprite.Group()
        player = Player()
        all_sprites.add(player)
        for i in range(10):
            r = rock(i+1) # 建立 rock 物件時傳入數字
            all_sprites.add(r)
            rocks.add(r)
    clock.tick(FPS)

#取得收入

    for event in pygame.event.get():
        key_pressed = pygame.key.get_pressed()
        if key_pressed[pygame.K_SPACE]:
            player.shoot()
            if event.type == pygame.QUIT:
                running = False
                
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    player.shoot()


            

#更新遊戲
    all_sprites.update()
    hits = pygame.sprite.groupcollide(rocks, bullets, True, True)
    for hit in hits:
        random.choice(expl_sounds).play()
        score += hit.radious
        expl = Explosion(hit.rect.center, 'lg')
        all_sprites.add(expl)
        if random.random() > 0.5:
            pow = Power(hit.rect.center)
            all_sprites.add(pow)
            powers.add(pow)
        new_rock()
        

    hits = pygame.sprite.spritecollide(player, rocks, True, pygame.sprite.collide_circle)
    for hit in hits:
        new_rock()
        player.health -= hit.radious * 2
        expl = Explosion(hit.rect.center, 'sm')
        all_sprites.add(expl)
        
        if player.health <= 0:
            death_expl = Explosion(player.rect.center, 'player')
            all_sprites.add(death_expl)
            die_sound.play()
            player.live -= 1
            player.health = 100
            player.hide()
    # 
    hits = pygame.sprite.spritecollide(player, powers, True)
    for hit in hits:
        if hit.type == 'shield':
            player.health += 20
            if player.health > 100:
                player.health = 100
            shield_sound.play()
        elif hit.type == 'gun':
            player.gunup()
            gun_sound.play()    

    if player.live == 0 and not (death_expl.alive()):
        show_init = True


 #畫面顯示
    screen.fill((WHITE))
    screen.blit(blackground_img, (0, 0)) 
    all_sprites.draw(screen)
    draw_text(screen,"score :" + str(score), 90, WIDTH-200, 100)
    draw_health(screen, player.health, 10 , 5)
    draw_lives(screen, player.live, player_mini_img, WIDTH -1650, 5)
    pygame.display.update()

pygame.quit()








